import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useTickets, useCreateTicket, useUpdateTicketStatus } from '@/lib/hooks/useTickets';
import { api } from '@/lib/services/api';

// Mock the API
jest.mock('@/lib/services/api', () => ({
  api: {
    getTickets: jest.fn(),
    createTicket: jest.fn(),
    updateTicketStatus: jest.fn(),
  }
}));

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        on: jest.fn(() => ({
          on: jest.fn(() => ({
            subscribe: jest.fn()
          }))
        }))
      })),
      subscribe: jest.fn()
    })),
    removeChannel: jest.fn()
  }))
}));

const mockTickets = [
  {
    id: '1',
    title: 'Test Ticket 1',
    status: 'open',
    priority: 'high',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    creator: { id: 'user1', email: '<EMAIL>' }
  },
  {
    id: '2',
    title: 'Test Ticket 2',
    status: 'in_progress',
    priority: 'medium',
    created_at: '2023-01-02T00:00:00Z',
    updated_at: '2023-01-02T00:00:00Z',
    creator: { id: 'user1', email: '<EMAIL>' }
  }
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useTickets', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches tickets successfully', async () => {
    (api.getTickets as jest.Mock).mockResolvedValue(mockTickets);

    const { result } = renderHook(() => useTickets(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockTickets);
    expect(api.getTickets).toHaveBeenCalledWith({});
  });

  it('passes filters to API call', async () => {
    (api.getTickets as jest.Mock).mockResolvedValue(mockTickets);

    const filters = { status: 'open', myTickets: true };
    const { result } = renderHook(() => useTickets(filters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(api.getTickets).toHaveBeenCalledWith(filters);
  });

  it('handles API errors', async () => {
    const errorMessage = 'Failed to fetch tickets';
    (api.getTickets as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useTickets(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(new Error(errorMessage));
  });
});

describe('useCreateTicket', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('creates ticket successfully', async () => {
    const newTicket = {
      id: '3',
      title: 'New Ticket',
      status: 'open',
      priority: 'medium',
      created_at: '2023-01-03T00:00:00Z',
      updated_at: '2023-01-03T00:00:00Z',
      creator: { id: 'user1', email: '<EMAIL>' }
    };

    (api.createTicket as jest.Mock).mockResolvedValue(newTicket);

    const { result } = renderHook(() => useCreateTicket(), {
      wrapper: createWrapper(),
    });

    const ticketData = {
      title: 'New Ticket',
      description: 'Test description',
      priority: 'medium'
    };

    result.current.mutate(ticketData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(newTicket);
    expect(api.createTicket).toHaveBeenCalledWith(ticketData);
  });

  it('handles creation errors', async () => {
    const errorMessage = 'Failed to create ticket';
    (api.createTicket as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useCreateTicket(), {
      wrapper: createWrapper(),
    });

    const ticketData = {
      title: 'New Ticket',
      description: 'Test description',
      priority: 'medium'
    };

    result.current.mutate(ticketData);

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(new Error(errorMessage));
  });
});

describe('useUpdateTicketStatus', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('updates ticket status successfully', async () => {
    const updatedTicket = {
      ...mockTickets[0],
      status: 'resolved',
      updated_at: '2023-01-03T00:00:00Z'
    };

    (api.updateTicketStatus as jest.Mock).mockResolvedValue(updatedTicket);

    const { result } = renderHook(() => useUpdateTicketStatus(), {
      wrapper: createWrapper(),
    });

    const updateData = { id: '1', status: 'resolved' };
    result.current.mutate(updateData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(updatedTicket);
    expect(api.updateTicketStatus).toHaveBeenCalledWith('1', 'resolved');
  });

  it('handles status update errors', async () => {
    const errorMessage = 'Failed to update ticket status';
    (api.updateTicketStatus as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useUpdateTicketStatus(), {
      wrapper: createWrapper(),
    });

    const updateData = { id: '1', status: 'resolved' };
    result.current.mutate(updateData);

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toEqual(new Error(errorMessage));
  });

  it('provides loading state during mutation', () => {
    (api.updateTicketStatus as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    const { result } = renderHook(() => useUpdateTicketStatus(), {
      wrapper: createWrapper(),
    });

    const updateData = { id: '1', status: 'resolved' };
    result.current.mutate(updateData);

    expect(result.current.isPending).toBe(true);
  });
});
