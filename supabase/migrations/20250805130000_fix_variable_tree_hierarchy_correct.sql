-- Migration: Fix get_variable_tree_with_context to build correct hierarchical tree
-- This replaces the previous function with one that properly builds parent-child relationships

CREATE OR REPLACE FUNCTION get_variable_tree_with_context(
    p_template_id INTEGER,
    p_user_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSONB;
    template_vars JSONB;
BEGIN
    -- Check if user has access to the template
    IF NOT EXISTS (
        SELECT 1 FROM global_job_templates 
        WHERE id = p_template_id AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'Template not found or access denied';
    END IF;

    -- Get template variables
    SELECT vars INTO template_vars
    FROM global_job_templates
    WHERE id = p_template_id;

    -- Build the complete tree structure with proper hierarchy
    WITH RECURSIVE 
    -- Get all categories with their hierarchy information
    all_categories AS (
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_category_id,
            c.variable_overrides,
            c.path,
            array_length(string_to_array(c.path::text, '.'), 1) - 1 as level
        FROM categories c
        WHERE c.user_id = p_user_id
    ),
    
    -- Get all datasets for each category
    all_datasets AS (
        SELECT 
            d.id,
            d.name,
            d.description,
            d.category_id,
            d.variable_overrides
        FROM datasets d
        WHERE d.user_id = p_user_id
        AND d.category_id IS NOT NULL
    ),
    
    -- Process variables for each category
    processed_categories AS (
        SELECT 
            ac.*,
            -- Process category variables (handle both old and new format)
            CASE 
                WHEN ac.variable_overrides IS NOT NULL THEN
                    CASE
                        -- New format with 'vars' array
                        WHEN ac.variable_overrides ? 'vars' THEN
                            (
                                SELECT jsonb_agg(
                                    var 
                                )
                                FROM jsonb_array_elements(ac.variable_overrides->'vars') AS var
                            )
                        -- Old format with direct key-value pairs
                        ELSE
                            (
                                SELECT jsonb_agg(
                                    jsonb_build_object(
                                        'name', kv.key,
                                        'data', kv.value
                                    
                                        
                                    )
                                )
                                FROM jsonb_each(ac.variable_overrides) AS kv
                                WHERE kv.key != 'vars'
                            )
                    END
                ELSE '[]'::jsonb
            END as category_variables,
            
            -- Get datasets for this category with their variables
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', d.id,
                        'name', d.name,
                        'description', COALESCE(d.description, ''),
                        'type', 'dataset',
                        'variables', 
                        CASE 
                            WHEN d.variable_overrides IS NOT NULL AND d.variable_overrides ? 'vars' THEN
                                (
                                    SELECT jsonb_agg(
                                        var
                                    )
                                    FROM jsonb_array_elements(d.variable_overrides->'vars') AS var
                                )
                            ELSE '[]'::jsonb
                        END
                    )
                    ORDER BY d.name
                )
                FROM all_datasets d
                WHERE d.category_id = ac.id
            ) as datasets
        FROM all_categories ac
    ),
    
    -- Build hierarchical tree recursively
    hierarchical_tree AS (
        -- Start with root categories (level 0)
        SELECT 
            pc.id,
            pc.name,
            pc.description,
            pc.parent_category_id,
            pc.level,
            jsonb_build_object(
                'id', pc.id,
                'name', pc.name,
                'description', COALESCE(pc.description, ''),
                'type', 'category',
                'level', pc.level,
                'variables', COALESCE(pc.category_variables, '[]'::jsonb),
                'datasets', COALESCE(pc.datasets, '[]'::jsonb),
                'children', '[]'::jsonb
            ) as node,
            ARRAY[pc.id] as path_array
        FROM processed_categories pc
        WHERE pc.parent_category_id IS NULL
        
        UNION ALL
        
        -- Add child categories recursively
        SELECT 
            pc.id,
            pc.name,
            pc.description,
            pc.parent_category_id,
            pc.level,
            jsonb_build_object(
                'id', pc.id,
                'name', pc.name,
                'description', COALESCE(pc.description, ''),
                'type', 'category',
                'level', pc.level,
                'variables', COALESCE(pc.category_variables, '[]'::jsonb),
                'datasets', COALESCE(pc.datasets, '[]'::jsonb),
                'children', '[]'::jsonb
            ) as node,
            ht.path_array || pc.id
        FROM processed_categories pc
        INNER JOIN hierarchical_tree ht ON pc.parent_category_id = ht.id
    ),
    
    -- Build nested tree structure by adding children to their parents
    nested_tree AS (
        SELECT 
            ht.id,
            ht.parent_category_id,
            ht.level,
            jsonb_set(
                ht.node,
                '{children}',
                COALESCE(
                    (
                        SELECT jsonb_agg(child.node ORDER BY (child.node->>'name'))
                        FROM hierarchical_tree child
                        WHERE child.parent_category_id = ht.id
                    ),
                    '[]'::jsonb
                )
            ) as final_node
        FROM hierarchical_tree ht
    )
    
    -- Build the final result
    SELECT jsonb_build_object(
        'template_id', p_template_id,
        'template_variables', 
        CASE 
            WHEN template_vars IS NOT NULL AND template_vars ? 'vars' THEN
                (
                    SELECT jsonb_agg(
                        var || jsonb_build_object(
                            'source_level', 'Template',
                            'is_active', true,
                            'is_overridden', false
                        )
                    )
                    FROM jsonb_array_elements(template_vars->'vars') AS var
                )
            ELSE '[]'::jsonb
        END,
        'tree', (
            SELECT jsonb_agg(nt.final_node ORDER BY (nt.final_node->>'name'))
            FROM nested_tree nt
            WHERE nt.parent_category_id IS NULL
        )
    ) INTO result;
    
    RETURN COALESCE(result, '{"template_id": null, "template_variables": [], "tree": []}'::jsonb);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building variable tree: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) IS 
'Returns the complete variable tree structure with proper hierarchy for a given template and user. Includes template variables, nested category hierarchy with children, dataset variables, and variable inheritance information.';
