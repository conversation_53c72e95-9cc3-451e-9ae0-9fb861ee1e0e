-- Migration: Extend get_variable_tree_with_context to include metadata and computed flags
-- This enhances the RPC function to include variable metadata (required, constraints, title, type)
-- and computed flags (editable_here, unset_required, defining path) for the variable hierarchy system

CREATE OR REPLACE FUNCTION get_variable_tree_with_context(
    p_template_id INTEGER,
    p_user_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSONB;
    template_vars JSONB;
BEGIN
    -- Check if user has access to the template
    IF NOT EXISTS (
        SELECT 1 FROM global_job_templates 
        WHERE id = p_template_id AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'Template not found or access denied';
    END IF;

    -- Get template variables
    SELECT vars INTO template_vars
    FROM global_job_templates
    WHERE id = p_template_id;

    WITH RECURSIVE 
    -- Get all categories with their hierarchy information
    all_categories AS (
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_category_id,
            c.variable_overrides,
            c.path,
            array_length(string_to_array(c.path::text, '.'), 1) - 1 as level
        FROM categories c
        WHERE c.user_id = p_user_id
    ),
    
    -- Get all datasets for each category
    all_datasets AS (
        SELECT 
            d.id,
            d.name,
            d.description,
            d.category_id,
            d.variable_overrides
        FROM datasets d
        WHERE d.user_id = p_user_id
        AND d.category_id IS NOT NULL
    ),
    
    -- Process variables for each category with enhanced metadata
    processed_categories AS (
        SELECT 
            ac.*,
            -- Process category variables with metadata
            CASE 
                WHEN ac.variable_overrides IS NOT NULL THEN
                    CASE
                        -- New format with 'vars' array
                        WHEN ac.variable_overrides ? 'vars' THEN
                            (
                                SELECT jsonb_agg(
                                    var || jsonb_build_object(
                                        'source_level', 'Category',
                                        'is_active', true,
                                        'is_overridden', false,
                                        'level', ac.level + 1,  -- Categories start at level 1
                                        'meta', jsonb_build_object(
                                            'required', COALESCE((var->'gui'->>'required')::boolean, false),
                                            'constraints', COALESCE(var->'gui'->'constraints', 
                                                jsonb_build_object('minLevel', 0, 'maxLevel', 999)),
                                            'title', COALESCE(var->>'title', var->'gui'->>'label', var->>'name'),
                                            'type', COALESCE(var->>'type', 'string')
                                        ),
                                        'editable_here', (
                                            COALESCE((var->'gui'->'constraints'->>'minLevel')::integer, 0) <= (ac.level + 1) AND
                                            (ac.level + 1) <= COALESCE((var->'gui'->'constraints'->>'maxLevel')::integer, 999)
                                        ),
                                        'unset_required', (
                                            COALESCE((var->'gui'->>'required')::boolean, false) AND 
                                            (var->'data' IS NULL OR var->'data' = 'null'::jsonb)
                                        )
                                    )
                                )
                                FROM jsonb_array_elements(ac.variable_overrides->'vars') AS var
                            )
                        -- Old format with direct key-value pairs
                        ELSE
                            (
                                SELECT jsonb_agg(
                                    jsonb_build_object(
                                        'name', kv.key,
                                        'data', kv.value,
                                        'source_level', 'Category',
                                        'is_active', true,
                                        'is_overridden', false,
                                        'level', ac.level + 1,
                                        'meta', jsonb_build_object(
                                            'required', false,
                                            'constraints', jsonb_build_object('minLevel', 0, 'maxLevel', 999),
                                            'title', kv.key,
                                            'type', 'string'
                                        ),
                                        'editable_here', true,
                                        'unset_required', false
                                    )
                                )
                                FROM jsonb_each(ac.variable_overrides) AS kv
                                WHERE kv.key != 'vars'
                            )
                    END
                ELSE '[]'::jsonb
            END as category_variables,
            
            -- Get datasets for this category with enhanced metadata
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', d.id,
                        'name', d.name,
                        'description', COALESCE(d.description, ''),
                        'type', 'dataset',
                        'level', 999,  -- Datasets always at level 999
                        'variables', 
                        CASE 
                            WHEN d.variable_overrides IS NOT NULL AND d.variable_overrides ? 'vars' THEN
                                (
                                    SELECT jsonb_agg(
                                        var || jsonb_build_object(
                                            'source_level', 'Dataset',
                                            'is_active', true,
                                            'is_overridden', false,
                                            'level', 999,
                                            'meta', jsonb_build_object(
                                                'required', COALESCE((var->'gui'->>'required')::boolean, false),
                                                'constraints', COALESCE(var->'gui'->'constraints', 
                                                    jsonb_build_object('minLevel', 0, 'maxLevel', 999)),
                                                'title', COALESCE(var->>'title', var->'gui'->>'label', var->>'name'),
                                                'type', COALESCE(var->>'type', 'string')
                                            ),
                                            'editable_here', (
                                                COALESCE((var->'gui'->'constraints'->>'minLevel')::integer, 0) <= 999 AND
                                                999 <= COALESCE((var->'gui'->'constraints'->>'maxLevel')::integer, 999)
                                            ),
                                            'unset_required', (
                                                COALESCE((var->'gui'->>'required')::boolean, false) AND 
                                                (var->'data' IS NULL OR var->'data' = 'null'::jsonb)
                                            )
                                        )
                                    )
                                    FROM jsonb_array_elements(d.variable_overrides->'vars') AS var
                                )
                            ELSE '[]'::jsonb
                        END
                    )
                    ORDER BY d.name
                )
                FROM all_datasets d
                WHERE d.category_id = ac.id
            ) as datasets
        FROM all_categories ac
    ),
    
    -- Build hierarchical tree recursively
    hierarchical_tree AS (
        -- Start with root categories
        SELECT 
            pc.id,
            pc.name,
            pc.description,
            pc.parent_category_id,
            pc.level,
            jsonb_build_object(
                'id', pc.id,
                'name', pc.name,
                'description', COALESCE(pc.description, ''),
                'type', 'category',
                'level', pc.level + 1,  -- Categories start at level 1
                'variables', COALESCE(pc.category_variables, '[]'::jsonb),
                'datasets', COALESCE(pc.datasets, '[]'::jsonb),
                'children', '[]'::jsonb
            ) as node,
            ARRAY[pc.id] as path_array
        FROM processed_categories pc
        WHERE pc.parent_category_id IS NULL
        
        UNION ALL
        
        -- Add child categories recursively
        SELECT 
            pc.id,
            pc.name,
            pc.description,
            pc.parent_category_id,
            pc.level,
            jsonb_build_object(
                'id', pc.id,
                'name', pc.name,
                'description', COALESCE(pc.description, ''),
                'type', 'category',
                'level', pc.level + 1,  -- Categories start at level 1
                'variables', COALESCE(pc.category_variables, '[]'::jsonb),
                'datasets', COALESCE(pc.datasets, '[]'::jsonb),
                'children', '[]'::jsonb
            ) as node,
            ht.path_array || pc.id
        FROM processed_categories pc
        INNER JOIN hierarchical_tree ht ON pc.parent_category_id = ht.id
    ),
    
    -- Build nested tree structure by adding children to their parents
    nested_tree AS (
        SELECT 
            ht.id,
            ht.parent_category_id,
            ht.level,
            jsonb_set(
                ht.node,
                '{children}',
                COALESCE(
                    (
                        SELECT jsonb_agg(child.node ORDER BY (child.node->>'name'))
                        FROM hierarchical_tree child
                        WHERE child.parent_category_id = ht.id
                    ),
                    '[]'::jsonb
                )
            ) as final_node
        FROM hierarchical_tree ht
    )
    
    -- Build the final result with enhanced template variables
    SELECT jsonb_build_object(
        'template_id', p_template_id,
        'template_variables', 
        CASE 
            WHEN template_vars IS NOT NULL AND template_vars ? 'vars' THEN
                (
                    SELECT jsonb_agg(
                        var || jsonb_build_object(
                            'source_level', 'Template',
                            'is_active', true,
                            'is_overridden', false,
                            'level', 0,  -- Template is level 0 (Global)
                            'meta', jsonb_build_object(
                                'required', COALESCE((var->'gui'->>'required')::boolean, false),
                                'constraints', COALESCE(var->'gui'->'constraints', 
                                    jsonb_build_object('minLevel', 0, 'maxLevel', 999)),
                                'title', COALESCE(var->>'title', var->'gui'->>'label', var->>'name'),
                                'type', COALESCE(var->>'type', 'string')
                            ),
                            'editable_here', (
                                COALESCE((var->'gui'->'constraints'->>'minLevel')::integer, 0) <= 0 AND
                                0 <= COALESCE((var->'gui'->'constraints'->>'maxLevel')::integer, 999)
                            ),
                            'unset_required', (
                                COALESCE((var->'gui'->>'required')::boolean, false) AND 
                                (var->'data' IS NULL OR var->'data' = 'null'::jsonb)
                            )
                        )
                    )
                    FROM jsonb_array_elements(template_vars->'vars') AS var
                )
            ELSE '[]'::jsonb
        END,
        'tree', (
            SELECT jsonb_agg(nt.final_node ORDER BY (nt.final_node->>'name'))
            FROM nested_tree nt
            WHERE nt.parent_category_id IS NULL
        )
    ) INTO result;
    
    RETURN COALESCE(result, '{"template_id": null, "template_variables": [], "tree": []}'::jsonb);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building variable tree with meta: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) TO authenticated;

-- Update comment to reflect new functionality
COMMENT ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) IS 
'Returns the complete variable tree structure with metadata and computed flags for a given template and user. Includes template variables (level 0), nested category hierarchy with children (level 1+), dataset variables (level 999), variable inheritance information, editability constraints, and required field validation.';
