-- Migration: Add variable metadata (required, constraints) to template variables
-- This migration adds missing gui.required and gui.constraints fields to existing template variables
-- and standardizes constraint field names to use minLevel/maxLevel

-- Create a function to update template variable metadata
CREATE OR REPLACE FUNCTION update_template_variable_metadata()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    template_record RECORD;
    updated_vars JSONB;
    var_element JSONB;
    updated_var JSONB;
    vars_array JSONB[];
    i INTEGER;
BEGIN
    -- Loop through all templates that have variables
    FOR template_record IN 
        SELECT id, vars 
        FROM global_job_templates 
        WHERE vars IS NOT NULL AND vars ? 'vars'
    LOOP
        -- Initialize array for updated variables
        vars_array := ARRAY[]::JSONB[];
        
        -- Process each variable in the vars array
        FOR i IN 0..jsonb_array_length(template_record.vars->'vars') - 1
        LOOP
            var_element := template_record.vars->'vars'->i;
            updated_var := var_element;
            
            -- Add gui object if it doesn't exist
            IF NOT (var_element ? 'gui') THEN
                updated_var := updated_var || jsonb_build_object('gui', '{}'::jsonb);
            END IF;
            
            -- Add required field if missing (default: false)
            IF NOT (updated_var->'gui' ? 'required') THEN
                updated_var := jsonb_set(
                    updated_var,
                    '{gui,required}',
                    'false'::jsonb
                );
            END IF;
            
            -- Handle constraints field
            IF NOT (updated_var->'gui' ? 'constraints') THEN
                -- Add default constraints if missing
                updated_var := jsonb_set(
                    updated_var,
                    '{gui,constraints}',
                    jsonb_build_object('minLevel', 0, 'maxLevel', 999)
                );
            ELSE
                -- Update existing constraints to use minLevel/maxLevel instead of min/max
                DECLARE
                    existing_constraints JSONB;
                    new_constraints JSONB;
                BEGIN
                    existing_constraints := updated_var->'gui'->'constraints';
                    new_constraints := existing_constraints;
                    
                    -- Convert min to minLevel if present
                    IF existing_constraints ? 'min' THEN
                        new_constraints := new_constraints || jsonb_build_object('minLevel', existing_constraints->'min');
                        new_constraints := new_constraints - 'min';
                    END IF;
                    
                    -- Convert max to maxLevel if present
                    IF existing_constraints ? 'max' THEN
                        new_constraints := new_constraints || jsonb_build_object('maxLevel', existing_constraints->'max');
                        new_constraints := new_constraints - 'max';
                    END IF;
                    
                    -- Ensure minLevel exists (default: 0)
                    IF NOT (new_constraints ? 'minLevel') THEN
                        new_constraints := new_constraints || jsonb_build_object('minLevel', 0);
                    END IF;
                    
                    -- Ensure maxLevel exists (default: 999)
                    IF NOT (new_constraints ? 'maxLevel') THEN
                        new_constraints := new_constraints || jsonb_build_object('maxLevel', 999);
                    END IF;
                    
                    updated_var := jsonb_set(
                        updated_var,
                        '{gui,constraints}',
                        new_constraints
                    );
                END;
            END IF;
            
            -- Add title field if missing (use gui.label or name as fallback)
            IF NOT (updated_var ? 'title') THEN
                IF updated_var->'gui' ? 'label' THEN
                    updated_var := updated_var || jsonb_build_object('title', updated_var->'gui'->'label');
                ELSE
                    updated_var := updated_var || jsonb_build_object('title', updated_var->'name');
                END IF;
            END IF;
            
            -- Add type field if missing (infer from component_id or default to 'string')
            IF NOT (updated_var ? 'type') THEN
                DECLARE
                    component_id TEXT;
                    inferred_type TEXT;
                BEGIN
                    component_id := updated_var->'gui'->>'component_id';
                    
                    -- Infer type from component_id
                    CASE component_id
                        WHEN 'Checkbox' THEN inferred_type := 'boolean';
                        WHEN 'CheckboxGroup' THEN inferred_type := 'array';
                        WHEN 'RadioButtons' THEN inferred_type := 'enum';
                        WHEN 'DatetimePicker' THEN inferred_type := 'datetime';
                        WHEN 'NumberInput' THEN inferred_type := 'number';
                        WHEN 'FilePicker' THEN inferred_type := 'file';
                        ELSE inferred_type := 'string';
                    END CASE;
                    
                    updated_var := updated_var || jsonb_build_object('type', inferred_type);
                END;
            END IF;
            
            -- Add updated variable to array
            vars_array := vars_array || updated_var;
        END LOOP;
        
        -- Build updated vars object
        updated_vars := template_record.vars || jsonb_build_object('vars', to_jsonb(vars_array));
        
        -- Update the template record
        UPDATE global_job_templates 
        SET vars = updated_vars
        WHERE id = template_record.id;
        
        RAISE NOTICE 'Updated template % (%) with % variables', 
            template_record.id, 
            (SELECT name FROM global_job_templates WHERE id = template_record.id),
            array_length(vars_array, 1);
    END LOOP;
END;
$$;

-- Execute the migration function
SELECT update_template_variable_metadata();

-- Drop the temporary function
DROP FUNCTION update_template_variable_metadata();

-- Add a comment to track this migration
COMMENT ON TABLE global_job_templates IS 'Updated 2025-08-23: Added variable metadata (required, constraints with minLevel/maxLevel, title, type) to template variables';
