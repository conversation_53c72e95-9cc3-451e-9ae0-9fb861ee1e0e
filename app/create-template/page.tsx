"use client"

import { useState } from 'react';
import { G<PERSON>, TextField, Button, Typography } from '@mui/material';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useRouter } from "next/navigation";
import { useCreateTemplate } from '@/lib/hooks/useTemplates';


export default function CreateTemplate() {
    const router = useRouter();
    const createTemplate = useCreateTemplate();
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        commented_json: '',
        commented_vars: ''
    });
    const [error, setError] = useState<string | null>(null);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        try {
            await createTemplate.mutateAsync(formData);
            router.push('/templates');
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to create template');
        }
    };

    return (
        <PageContainer>
            <Typography variant="h4" gutterBottom>
                Create New Global Job Template
            </Typography>
            <form onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <TextField
                            fullWidth
                            label="Name"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            required
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField
                            fullWidth
                            label="Description"
                            value={formData.description}
                            onChange={(e) => setFormData({...formData, description: e.target.value})}
                            multiline
                            rows={4}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField
                            fullWidth
                            label="Commented JSON"
                            value={formData.commented_json}
                            onChange={(e) => setFormData({...formData, commented_json: e.target.value})}
                            multiline
                            rows={8}
                            required
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <TextField
                            fullWidth
                            label="Commented Variables"
                            value={formData.commented_vars}
                            onChange={(e) => setFormData({...formData, commented_vars: e.target.value})}
                            multiline
                            rows={4}
                            required
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Button type="submit" variant="contained" color="primary">
                            Create Template
                        </Button>
                    </Grid>
                </Grid>
            </form>

            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Template Creation Error"
            />
        </PageContainer>
    );
}
