import { Variable } from './types';

export function parseVariableValue(value: string): any {
    const trimmedValue = value.trim();
    if (trimmedValue === '') return '';

    if (trimmedValue.startsWith('[') || trimmedValue.startsWith('{')) {
        return JSON.parse(trimmedValue);
    }

    if (trimmedValue.toLowerCase() === 'true') return true;
    if (trimmedValue.toLowerCase() === 'false') return false;

    const numberValue = Number(trimmedValue);
    if (!isNaN(numberValue) && trimmedValue !== '') {
        return numberValue;
    }

    return trimmedValue;
}

export function validateVariableValue(value: string): boolean {
    try {
        parseVariableValue(value);
        return true;
    } catch (e) {
        return false;
    }
}

export function formatVariablesForApi(variables: Variable[]) {
    return variables
        .map(({ name, value }) => ({
            data: parseVariableValue(value),
            name: name,
            links: []
        }))
        .filter(v => v.name.trim());
}
