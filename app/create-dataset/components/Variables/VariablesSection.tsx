import { Box, Button, Typography, Alert } from '@mui/material';
import { Variable } from '../../types';
import { VariableRow } from './VariableRow';

interface VariablesSectionProps {
    variables: Variable[];
    hasValidationError: boolean;
    onVariableChange: (index: number, field: 'name' | 'value', value: string) => void;
    onVariableDelete: (index: number) => void;
    onAddVariable: () => void;
}

export function VariablesSection({
    variables,
    hasValidationError,
    onVariableChange,
    onVariableDelete,
    onAddVariable
}: VariablesSectionProps) {
    return (
        <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom>Variables</Typography>
            {hasValidationError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    Please fix invalid variable values before saving
                </Alert>
            )}
            {variables.map((variable, index) => (
                <VariableRow
                    key={index}
                    variable={variable}
                    index={index}
                    onVariableChange={onVariableChange}
                    onVariableDelete={onVariableDelete}
                />
            ))}
            <Button
                variant="outlined"
                onClick={onAddVariable}
                sx={{ mt: 2 }}
            >
                Add Variable
            </Button>
        </Box>
    );
}
