"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Button, Box, Container, Paper, Stack, CircularProgress, Alert } from '@mui/material'
import SaveIcon from '@mui/icons-material/Save'
import { PageContainer } from '@/components/layout/PageContainer'
import { ErrorDialog } from '@/components/dialogs/ErrorDialog'
import TemplateRenderer from '@/components/template/TemplateRenderer'
import { useGetTemplateVars, useSaveTemplateVars } from '@/lib/hooks/useTemplateVars'
import { useTemplateStore } from '@/lib/stores/templateStore'

interface TemplatePageProps {
  params: {
    id: string
  }
}

export default function TemplatePage({ params }: TemplatePageProps) {
  const { id } = params
  const { data: template, isLoading, error: fetchError } = useGetTemplateVars(id)
  const { templateVars } = useTemplateStore()
  const saveTemplate = useSaveTemplateVars()
  const [error, setError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (fetchError) {
      setError(fetchError.message || 'Failed to load template')
    }
  }, [fetchError])

  const handleSave = async () => {
    // Check for validation errors
    if (useTemplateStore.getState().hasValidationErrors()) {
      setError('Please fix all validation errors before saving');
      return;
    }

    try {
      setIsSaving(true)
      await saveTemplate.mutateAsync({
        templateId: id,
        updatedTemplate: {
          ...template,
          vars: templateVars
        }
      })
      setError(null)
    } catch (err: any) {
      setError(err.message || 'Failed to save template')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      </PageContainer>
    )
  }

  if (!template) {
    return (
      <PageContainer>
        <Alert severity="error">Template not found</Alert>
      </PageContainer>
    )
  }

  return (
    <PageContainer>
      <Paper elevation={0} sx={{ p: 3, mb: 3 }}>
        <Stack 
          direction="row" 
          justifyContent="space-between" 
          alignItems="center"
        >
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              {template.name}
            </Typography>
            {template.description && (
              <Typography variant="body1" color="text.secondary">
                {template.description}
              </Typography>
            )}
          </Box>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={isSaving }
            startIcon={<SaveIcon />}
            size="large"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </Stack>
      </Paper>
      
      <TemplateRenderer templateId={id} />

      <ErrorDialog
        open={!!error}
        onClose={() => setError(null)}
        error={error}
        title="Error"
      />
    </PageContainer>
  )
}
