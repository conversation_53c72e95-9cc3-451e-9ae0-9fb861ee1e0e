"use client"

import { useState, useEffect } from 'react';
import { Box, Typography, Button } from '@mui/material';
import TemplateTable from '@/components/TemplateTable';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import Link from 'next/link';
import { useTemplates, useDeleteTemplate } from '@/lib/hooks/useTemplates';

export default function Templates() {
    const { data: templatesData, isLoading, error: fetchError } = useTemplates();
    const deleteTemplate = useDeleteTemplate();
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (fetchError) {
            setError(fetchError.message || 'Failed to load templates');
        }
    }, [fetchError]);

    const handleDeleteTemplate = async (id: string) => {
        try {
            await deleteTemplate.mutateAsync(id);
        } catch (error: any) {
            if (error?.code === '23503') {
                const referencedTable = error?.details?.match(/table "(\w+)"/)?.[1];
                const readableTableName = referencedTable
                    ?.split('_')
                    .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
                setError(`This template cannot be deleted because it is still referenced in ${readableTableName}`);
            } else {
                setError(error?.message || 'Failed to delete template');
            }
        }
    };

    if (isLoading) return <div>Loading...</div>;
    return (
        <PageContainer>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" gutterBottom>
                    Your Global Job Templates
                </Typography>
                <Link href="/create-template" passHref>
                    <Button variant="contained" color="primary">
                        Create New Template
                    </Button>
                </Link>
            </Box>
            <TemplateTable
                templates={templatesData?.data || []}
                onDeleteTemplate={handleDeleteTemplate}
            />
            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Template Error"
            />
        </PageContainer>
    );
}
