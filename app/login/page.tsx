import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, TextField, Button, Alert, Link as MuiLink } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { headers } from "next/headers";

export default function Login({
  searchParams,
}: {
  searchParams: { signup: boolean, error: boolean };
}) {
  const signIn = async (formData: FormData) => {
      "use server";
      const supabase = createClient();
      const { error } = await supabase.auth.signInWithPassword({
          email: formData.get("email") as string,
          password: formData.get("password") as string,
      });

      if (error) {
          return redirect("/login?error=true");
      }
      return redirect("/");
  };

  const signUp = async (formData: FormData) => {
      "use server";
      const origin = headers().get("origin");
      const supabase = createClient();
      const { error } = await supabase.auth.signUp({
          email: formData.get("email") as string,
          password: formData.get("password") as string,
          options: {
              emailRedirectTo: `${origin}/auth/callback`,
          },
      });

      if (error) {
          return redirect("/login?error=true");
      }
      return redirect("/login?signup=true");
  };

  return (
      <Container component="main" maxWidth="xs">
          <Box sx={{ mt: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <MuiLink href="/" sx={{ position: 'absolute', left: 8, top: 8, display: 'flex', alignItems: 'center', textDecoration: 'none', color: 'text.primary' }}>
                  <ArrowBackIcon sx={{ mr: 1 }} />
                  Back
              </MuiLink>
              <Typography component="h1" variant="h5">Sign in</Typography>
              <Box component="form" noValidate sx={{ mt: 1 }}>
                  <TextField margin="normal" required fullWidth id="email" label="Email Address" name="email" autoComplete="email" autoFocus />
                  <TextField margin="normal" required fullWidth name="password" label="Password" type="password" id="password" autoComplete="current-password" />
                  {searchParams?.error && <Alert severity="error" sx={{ mt: 2 }}>Could not authenticate user</Alert>}
                  {searchParams?.signup && <Alert severity="success" sx={{ mt: 2 }}>Check email to continue sign in process</Alert>}
                  <Button type="submit" fullWidth variant="contained" sx={{ mt: 3, mb: 2 }} formAction={signIn}>Sign In</Button>
                  <Button type="submit" fullWidth variant="outlined" sx={{ mb: 2 }} formAction={signUp}>Sign Up</Button>
              </Box>
          </Box>
      </Container>
  );
}