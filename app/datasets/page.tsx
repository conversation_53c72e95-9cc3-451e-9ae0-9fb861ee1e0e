"use client"

import { useState,useEffect } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { DatasetTable } from '@/components/dataset-table/DatasetTable';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import Link from 'next/link';
import { useDatasets, useDeleteDataset } from '@/lib/hooks/useDatasets';

export default function Datasets() {
    const { data: datasetsData, isLoading, error: fetchError } = useDatasets();
    const deleteDataset = useDeleteDataset();
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (fetchError) {
            setError(fetchError.message || 'Failed to load datasets');
        }
    }, [fetchError]);

    const handleDelete = async (dataset: { id: string }) => {
        try {
            await deleteDataset.mutateAsync(dataset.id);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete dataset');
        }
    };

    if (isLoading) return <div>Loading...</div>;

    return (
        <PageContainer>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" gutterBottom>
                    Datasets
                </Typography>
                <Link href="/create-dataset" passHref>
                    <Button variant="contained" color="primary">
                        Create New Dataset
                    </Button>
                </Link>
            </Box>
            <DatasetTable
                datasets={datasetsData?.data || []}
                onDelete={handleDelete}
            />
            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Dataset Error"
            />
        </PageContainer>
    );
}