"use client"

import { Suspense } from 'react';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Box, Typography, CircularProgress, Alert, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { VariableTreeView } from '@/components/template/VariableTreeView';
import { useTemplates } from '@/lib/hooks/useTemplates';

function VariableTreeContent() {
    const searchParams = useSearchParams();
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    
    const { data: templatesData, isLoading: templatesLoading, error: templatesError } = useTemplates();
    // VariableTree data is fetched within VariableTreeView; keep page focused on template selection

    useEffect(() => {
        if (templatesError) {
            setError(templatesError.message || 'Failed to load templates');
        }
    }, [templatesError]);

    // Any errors from the VariableTreeView are handled inside that component

    // Read templateId from URL parameters or auto-select first template
    useEffect(() => {
        const urlTemplateId = searchParams.get('templateId');
        if (urlTemplateId) {
            setSelectedTemplateId(urlTemplateId);
        } else if (templatesData?.data && templatesData.data.length > 0 && !selectedTemplateId) {
            setSelectedTemplateId(templatesData.data[0].id.toString());
        }
    }, [templatesData, selectedTemplateId, searchParams]);

    const handleTemplateChange = (templateId: string) => {
        setSelectedTemplateId(templateId);
        setError(null);
    };

    if (templatesLoading) {
        return (
            <PageContainer>
                <Box display="flex" justifyContent="center" p={4}>
                    <CircularProgress />
                </Box>
            </PageContainer>
        );
    }

    if (!templatesData?.data || templatesData.data.length === 0) {
        return (
            <PageContainer>
                <Alert severity="info">No templates found. Create a template first to view its variable tree.</Alert>
            </PageContainer>
        );
    }

    const selectedTemplate = templatesData?.data?.find((t: any) => t.id.toString() === selectedTemplateId);
    const selectedTemplateName = selectedTemplate?.name || '';

    return (
        <PageContainer>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" component="h1">
                    Variable Tree
                </Typography>
            </Box>

            {selectedTemplateId && (
                <VariableTreeView
                    templateId={parseInt(selectedTemplateId)}
                    templateName={selectedTemplateName}
                    templateChangeKey={selectedTemplateId}
                    templateControl={
                        <FormControl size="small" sx={{ minWidth: 220, width: { xs: '100%', sm: 260 } }}>
                            <InputLabel id="template-select-label">Select Template</InputLabel>
                            <Select
                                labelId="template-select-label"
                                value={selectedTemplateId}
                                label="Select Template"
                                onChange={(e) => handleTemplateChange(e.target.value)}
                            >
                                {templatesData.data.map((template: any) => (
                                    <MenuItem key={template.id} value={template.id.toString()}>
                                        {template.name}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    }
                />
            )}

            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Variable Tree Error"
            />
        </PageContainer>
    );
}

export default function VariableTreePage() {
    return (
        <Suspense fallback={
            <PageContainer>
                <Box display="flex" justifyContent="center" p={4}>
                    <CircularProgress />
                </Box>
            </PageContainer>
        }>
            <VariableTreeContent />
        </Suspense>
    );
}
