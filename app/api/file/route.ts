import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const formData = await request.formData();
    const file = formData.get('file');
    const originalPath = formData.get('originalPath');

    if (!file) {
        return NextResponse.json({ error: 'Files are required' }, { status: 400 });
    }

    // Type guard to ensure file is a File object, not a string
    if (!(file instanceof File)) {
        return NextResponse.json({ error: 'Invalid file format' }, { status: 400 });
    }

    const buffer = await file.arrayBuffer();
    const fileName = (originalPath as string) || file.name;
    const fullPath = `${userId}/${fileName}`;

    const bucketName = process.env.NEXT_PUBLIC_BUCKET_NAME;
    if (!bucketName) {
        return NextResponse.json({ error: 'Storage bucket not configured' }, { status: 500 });
    }

    const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(fullPath, buffer, {
            contentType: file.type,
            upsert: true,
        });

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ success: true });
});

export const GET = withAuth(async (userId) => {
    const supabase = createClient();

    const bucketName = process.env.NEXT_PUBLIC_BUCKET_NAME;
    if (!bucketName) {
        return NextResponse.json({ error: 'Storage bucket not configured' }, { status: 500 });
    }

    async function listAllFiles(path: string): Promise<any[]> {
        const { data, error } = await supabase.storage
            .from(bucketName!)
            .list(path);

        if (error) {
            throw error;
        }

        let allFiles: any[] = [];
        for (const item of data) {
            if (item.metadata) {
                allFiles.push({
                    ...item,
                    fullPath: `${path}/${item.name}`
                });
            } else {
                const subFiles = await listAllFiles(`${path}/${item.name}`);
                allFiles = allFiles.concat(subFiles);
            }
        }
        return allFiles;
    }

    try {
        const files = await listAllFiles(userId);
        return NextResponse.json({ files: files || [] });
    } catch (error) {
        return NextResponse.json({ error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
    }
});

export const DELETE = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');

    if (!filePath) {
        return NextResponse.json({ error: 'File path is required' }, { status: 400 });
    }

    const bucketName = process.env.NEXT_PUBLIC_BUCKET_NAME;
    if (!bucketName) {
        return NextResponse.json({ error: 'Storage bucket not configured' }, { status: 500 });
    }

    const { error: storageError } = await supabase.storage
        .from(bucketName)
        .remove([filePath]);

    if (storageError) {
        return NextResponse.json({ error: storageError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
});
