// app/api/logs/database/route.ts
import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId) => {
  const supabase = createClient();

  // Fetch all log fields from the database
  const { data, error } = await supabase
    .from('log_fields')
    .select('group_name, field_data');

  if (error) {
    console.error('Error fetching log fields:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  // Transform the data into the expected format
  // The frontend expects an object with group names as keys and arrays of fields as values
  const logDatabase: Record<string, any[]> = {};

  data.forEach(item => {
    // Parse the field_data JSON if it's a string
    const fieldData = typeof item.field_data === 'string'
      ? JSON.parse(item.field_data)
      : item.field_data;

    // Add the group to the database object
    logDatabase[item.group_name] = fieldData;
  });

  return NextResponse.json({ success: true, data: logDatabase });
});
