import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
import JSZip from 'jszip';

export const runtime = 'nodejs';

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const jobId = Array.isArray(params.id) ? params.id[0] : params.id; // Renamed batchId to jobId

    if (!jobId) { // Renamed batchId to jobId
        return NextResponse.json({ error: 'Job ID is required' }, { status: 400 }); // Updated error message
    }

    // First get all tasks in the job (formerly jobs in batch)
    const { data: tasks, error: tasksError } = await supabase // Renamed jobs to tasks, jobsError to tasksError
        .from('tasks') // Renamed jobs to tasks
        .select('id')
        .eq('job_id', jobId) // Renamed batch_id to job_id, batchId to jobId
        .eq('user_id', userId)
        .is('bulk_job_type', null); // Filter out bulk jobs

    if (tasksError) { // Renamed jobsError to tasksError
        console.error('Error fetching tasks:', tasksError); // Renamed jobs to tasks
        return NextResponse.json({ error: tasksError.message }, { status: 500 });
    }

    if (!tasks || tasks.length === 0) { // Renamed jobs to tasks
        return NextResponse.json({ error: 'No tasks found in job' }, { status: 404 }); // Renamed jobs to tasks, batch to job
    }

    // Get all visible results for all tasks in the job
    const taskIds = tasks.map(task => task.id); // Renamed jobIds to taskIds, jobs to tasks, job to task
    const { data: results, error: resultsError } = await supabase
        .from('task_results') // This table name is correct as per JSON (only column renamed)
        .select('*')
        .in('task_id', taskIds) // Renamed job_id to task_id, jobIds to taskIds
        .eq('visible', true);

    if (resultsError) {
        console.error('Error fetching results:', resultsError);
        return NextResponse.json({ error: resultsError.message }, { status: 500 });
    }

    if (!results || results.length === 0) {
        return NextResponse.json({ error: 'No visible results found' }, { status: 404 });
    }

    console.log(`Found ${results.length} visible results for job ${jobId}`); // Renamed batch to job, batchId to jobId

    const zip = new JSZip();
    for (const result of results) {
        try {
            // Clean up the storage path
            let storagePath = result.file_path;
            if (storagePath.startsWith('/')) {
                storagePath = storagePath.substring(1);
            }

            const { data, error: downloadError } = await supabase.storage
                .from(process.env.NEXT_PUBLIC_RESULT_BUCKET_NAME || 'job-results')
                .download(storagePath);

            if (downloadError) {
                console.error(`Error downloading file ${result.file_name}:`, downloadError);
                continue;
            }

            let fileData;
            if (typeof data.arrayBuffer === 'function') {
                const arrayBuffer = await data.arrayBuffer();
                fileData = new Uint8Array(arrayBuffer);
            } else {
                fileData = data;
            }

            // Organize files by task ID and type to maintain structure
            const taskFolder = `task_${result.task_id}`; // Renamed jobFolder to taskFolder, result.job_id to result.task_id
            const typeFolder = result.file_type || 'other';
            zip.file(`${taskFolder}/${typeFolder}/${result.file_name}`, fileData); // Used taskFolder

            console.log(`Added ${result.file_name} to zip (${result.file_type})`);
        } catch (error) {
            console.error(`Error processing file ${result.file_name}:`, error);
        }
    }

    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });

    return new NextResponse(zipContent, {
        headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': `attachment; filename="job_${jobId}_results.zip"` // Renamed batch to job, batchId to jobId
        }
    });
});