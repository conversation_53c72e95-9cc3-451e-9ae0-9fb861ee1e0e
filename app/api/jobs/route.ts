import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
// Removed unused import: import { JobResult, JobWithRelations } from '@/types/jobs';




export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { name, description, status } = await request.json();

    const { data, error } = await supabase
        .from('jobs')
        .insert({ user_id: userId, name, description, status })
        .select();

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const pageParam = searchParams.get('page');
    const itemsPerPageParam = searchParams.get('itemsPerPage');

    const page = pageParam ? parseInt(pageParam) : 1;
    const itemsPerPage = itemsPerPageParam ? parseInt(itemsPerPageParam) : 10;
    const start = (page - 1) * itemsPerPage;
    const end = start + itemsPerPage - 1;

    // Step 1: Fetch batches with count, without embedded jobs
    const { data: jobsData, error: batchesError, count } = await supabase
        .from('jobs')
        .select(`*`, { count: 'exact' }) // Select only batch fields
        .eq('user_id', userId)
        .range(start, end)
        .order('created_at', { ascending: false });

    if (batchesError) {
        console.error("Error fetching batches:", batchesError);
        return NextResponse.json({ error: batchesError.message }, { status: 500 });
    }

    if (!jobsData || jobsData.length === 0) {
        return NextResponse.json({ success: true, data: [], count: 0 });
    }

    // Step 2: Get IDs of the fetched batches
    const jobIds = jobsData.map(batch => batch.id);

    // Step 3: Fetch associated non-bulk jobs and their visible results for these batches
    // Step 3: Fetch associated non-bulk jobs and their results for these batches
    const { data: tasksData, error: jobsError } = await supabase
        .from('tasks')
        .select(
            `
              id,
              job_id,
              global_job_template:global_job_templates(*),
              dataset:datasets(name, description),
              task_results (id, file_name, file_type, visible)
            `
        )
        .in('job_id', jobIds) // Column batch_id renamed to job_id in tasks table
        .eq('user_id', userId)
        .is('bulk_job_type', null); // Filter out bulk jobs
    if (jobsError) {
        console.error("Error fetching task for job:", jobsError);
        // Return batches without jobs if jobs fetch fails
        return NextResponse.json({ success: true, data: jobsData, count });
    }

    // Step 4: Map jobs back to their respective batches
    // Step 4: Map jobs back to their respective batches, using inferred types
    const tasksByJobId = (tasksData || []).reduce((acc, task) => {
        // Let TS infer 'job' type from jobsData
        const jobId = task.job_id; // Property batch_id renamed to job_id
        if (!jobId) return acc; // Skip if batch_id is missing

        if (!acc[jobId]) {
            acc[jobId] = [];
        }

        // Filter results reliably in code
        const visibleResults = (task.task_results || []).filter(result => result.visible);

        // Create a new job object with filtered results
        console.log(`Processing task with ID: ${task.id} taks data:`, task);
        const processedJob = {
            ...task,
            // Adjust template/dataset if needed (accessing [0] if they are arrays)
            global_job_template: task.global_job_template ? {
                name: Array.isArray(task.global_job_template)
                    ? task.global_job_template[0]?.name
                    : (task.global_job_template as any)?.name
            } : null,
            dataset: task.dataset ? {
                name: Array.isArray(task.dataset)
                    ? task.dataset[0]?.name
                    : (task.dataset as any)?.name,
                description: Array.isArray(task.dataset)
                    ? task.dataset[0]?.description
                    : (task.dataset as any)?.description
            } : null,
            task_results: visibleResults
        };
        console.log(`Processed job:`, processedJob);
        acc[jobId].push(processedJob);

        return acc;
    }, {} as Record<string, any[]>); // Use a more generic accumulator type initially

    // Step 5: Combine batch data with their correctly processed jobs array
    const combinedData = jobsData.map(batch => ({
        ...batch, // Include all original batch fields
        jobs: tasksByJobId[batch.id] || [] // Assign the processed jobs array
    }));

    return NextResponse.json({ success: true, data: combinedData, count });
});

export const PUT = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { id, name, description, status } = await request.json();

    const { data, error } = await supabase
        .from('jobs')
        .update({ name, description, status })
        .eq('id', id)
        .eq('user_id', userId)
        .select();

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const DELETE = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
        return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    const { error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
});
