import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';
import {
  sanitizeVariableOverrideMap,
  deriveGuiConfig,
  deriveLinks,
  deriveComponentName,
} from '@/lib/utils/variableOverrides';

/**
 * PUT /api/variable-tree/category/[id]
 * Updates category variable overrides in the Variable Tree system
 *
 * Body: { variableOverrides: { [variableName: string]: any } }
 *
 * This endpoint handles variable override updates for categories.
 * Category variable overrides are stored in categories.variable_overrides field.
 */

/**
 * DELETE /api/variable-tree/category/[id]?name=VARIABLE_NAME
 * Removes a category variable override (resets to inherited/template value)
 *
 * Query params: name - the variable name to remove
 *
 * This endpoint removes a variable override from the category's variable_overrides.vars array,
 * effectively resetting it to inherit from template or higher-level categories.
 */
export const PUT = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const { variableOverrides } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    if (!variableOverrides || typeof variableOverrides !== 'object') {
      return NextResponse.json(
        { error: 'variableOverrides is required and must be an object' },
        { status: 400 }
      );
    }

    // First, get the current category to verify ownership and get current overrides
    const { data: currentCategory, error: fetchError } = await supabase
      .from('categories')
      .select('variable_overrides, user_id, path')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching category:', fetchError);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentCategory.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current variable overrides structure
    const currentOverrides = currentCategory.variable_overrides || { vars: [] };
    const currentVarsList = Array.isArray(currentOverrides.vars) ? currentOverrides.vars : [];

    // Calculate category level from path (categories start at level 1)
    const categoryLevel = currentCategory.path ?
      currentCategory.path.toString().split('.').length : 1;

    // Get template variables to check constraints for new variables
    const { data: templateData, error: templateError } = await supabase
      .from('global_job_templates')
      .select('vars')
      .eq('user_id', userId)
      .limit(1)
      .single();

    const templateVars = templateData?.vars?.vars || [];

    // Validate level constraints for each variable being updated
    for (const [variableName, variableValue] of Object.entries(variableOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      let constraints = null;

      if (existingVariable && existingVariable.gui?.constraints) {
        constraints = existingVariable.gui.constraints;
      } else {
        // Check template for constraints if not found in category
        const templateVar = templateVars.find((v: any) => v.name === variableName);
        if (templateVar && templateVar.gui?.constraints) {
          constraints = templateVar.gui.constraints;
        }
      }

      if (constraints) {
        const minLevel = constraints.minLevel ?? 0;
        const maxLevel = constraints.maxLevel ?? 999;

        if (categoryLevel < minLevel || categoryLevel > maxLevel) {
          return NextResponse.json(
            {
              error: `Variable '${variableName}' cannot be edited at category level (${categoryLevel}). Allowed levels: ${minLevel}-${maxLevel}`
            },
            { status: 400 }
          );
        }
      }
    }

    const sanitizedOverrides = sanitizeVariableOverrideMap(variableOverrides, {
      templateVars,
      currentVars: currentVarsList,
    });

    // Update variables with new values (same approach as template API)
    const updatedVarsList = currentVarsList.map((variable: any) => {
      if (sanitizedOverrides.hasOwnProperty(variable.name)) {
        return {
          ...variable,
          data: sanitizedOverrides[variable.name]
        };
      }
      return variable;
    });

    // Add new variables that don't exist yet
    for (const [variableName, sanitizedValue] of Object.entries(sanitizedOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (!existingVariable) {
        // Create new variable with minimal structure
        updatedVarsList.push({
          name: variableName,
          data: sanitizedValue,
          links: deriveLinks(variableName, templateVars, currentVarsList),
          gui: deriveGuiConfig(variableName, templateVars, currentVarsList),
          component_name: deriveComponentName(variableName, templateVars, currentVarsList),
        });
      }
    }

    // Create the updated overrides structure
    const updatedOverrides = {
      ...currentOverrides,
      vars: updatedVarsList
    };

    // Update the category with new variable overrides
    const { data: updatedCategory, error: updateError } = await supabase
      .from('categories')
      .update({
        variable_overrides: updatedOverrides
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('id, name, variable_overrides')
      .single();

    if (updateError) {
      console.error('Error updating category variables:', updateError);
      return NextResponse.json(
        { error: 'Failed to update category variables' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedCategory.id,
        name: updatedCategory.name,
        updatedVariables: Object.keys(sanitizedOverrides),
        totalOverrides: updatedVarsList.length,
        variable_overrides: updatedOverrides
      }
    });

  } catch (error) {
    console.error('Category variable update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const DELETE = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const url = new URL(request.url);
    const variableName = url.searchParams.get('name');

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    if (!variableName) {
      return NextResponse.json(
        { error: 'Variable name is required as query parameter' },
        { status: 400 }
      );
    }

    // First, get the current category to verify ownership and get current overrides
    const { data: currentCategory, error: fetchError } = await supabase
      .from('categories')
      .select('variable_overrides, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching category:', fetchError);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentCategory.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current variable overrides structure
    const currentOverrides = currentCategory.variable_overrides || { vars: [] };
    const currentVarsList = Array.isArray(currentOverrides.vars) ? currentOverrides.vars : [];

    // Check if variable exists
    const variableExists = currentVarsList.some((v: any) => v.name === variableName);
    if (!variableExists) {
      return NextResponse.json(
        { error: 'Variable override not found' },
        { status: 404 }
      );
    }

    // Remove the variable from the list
    const updatedVarsList = currentVarsList.filter((v: any) => v.name !== variableName);

    // Create the updated overrides structure
    const updatedOverrides = {
      ...currentOverrides,
      vars: updatedVarsList
    };

    // Update the category with new variable overrides
    const { data: updatedCategory, error: updateError } = await supabase
      .from('categories')
      .update({
        variable_overrides: updatedOverrides
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('id, name, variable_overrides')
      .single();

    if (updateError) {
      console.error('Error removing category variable override:', updateError);
      return NextResponse.json(
        { error: 'Failed to remove category variable override' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedCategory.id,
        name: updatedCategory.name,
        removedVariable: variableName,
        totalOverrides: updatedVarsList.length,
        variable_overrides: updatedOverrides
      }
    });

  } catch (error) {
    console.error('Category variable removal error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
