import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';
import {
  sanitizeVariableOverrideMap,
  deriveGuiConfig,
  deriveLinks,
  deriveComponentName,
} from '@/lib/utils/variableOverrides';

/**
 * PUT /api/variable-tree/dataset/[id]
 * Updates dataset variable overrides in the Variable Tree system
 *
 * Body: { variableOverrides: { [variableName: string]: any } }
 *
 * This endpoint handles variable override updates for datasets.
 * Dataset variable overrides are stored in datasets.variable_overrides field.
 */

/**
 * DELETE /api/variable-tree/dataset/[id]?name=VARIABLE_NAME
 * Removes a dataset variable override (resets to inherited value)
 *
 * Query params: name - the variable name to remove
 *
 * This endpoint removes a variable override from the dataset's variable_overrides.vars array,
 * effectively resetting it to inherit from category or template values.
 */
export const PUT = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const { variableOverrides } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Dataset ID is required' },
        { status: 400 }
      );
    }

    if (!variableOverrides || typeof variableOverrides !== 'object') {
      return NextResponse.json(
        { error: 'variableOverrides is required and must be an object' },
        { status: 400 }
      );
    }

    // First, get the current dataset to verify ownership and get current overrides
    const { data: currentDataset, error: fetchError } = await supabase
      .from('datasets')
      .select('variable_overrides, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching dataset:', fetchError);
      return NextResponse.json(
        { error: 'Dataset not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentDataset.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current variable overrides structure
    const currentOverrides = currentDataset.variable_overrides || { vars: [] };
    const currentVarsList = Array.isArray(currentOverrides.vars) ? currentOverrides.vars : [];

    // Dataset level is always 999 (highest priority)
    const datasetLevel = 999;

    // Get template variables to check constraints for new variables
    const { data: templateData, error: templateError } = await supabase
      .from('global_job_templates')
      .select('vars')
      .eq('user_id', userId)
      .limit(1)
      .single();

    const templateVars = templateData?.vars?.vars || [];

    // Validate level constraints for each variable being updated
    for (const [variableName, variableValue] of Object.entries(variableOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      let constraints = null;

      if (existingVariable && existingVariable.gui?.constraints) {
        constraints = existingVariable.gui.constraints;
      } else {
        // Check template for constraints if not found in dataset
        const templateVar = templateVars.find((v: any) => v.name === variableName);
        if (templateVar && templateVar.gui?.constraints) {
          constraints = templateVar.gui.constraints;
        }
      }

      if (constraints) {
        const minLevel = constraints.minLevel ?? 0;
        const maxLevel = constraints.maxLevel ?? 999;

        if (datasetLevel < minLevel || datasetLevel > maxLevel) {
          return NextResponse.json(
            {
              error: `Variable '${variableName}' cannot be edited at dataset level (${datasetLevel}). Allowed levels: ${minLevel}-${maxLevel}`
            },
            { status: 400 }
          );
        }
      }
    }

    const sanitizedOverrides = sanitizeVariableOverrideMap(variableOverrides, {
      templateVars,
      currentVars: currentVarsList,
    });

    // Update variables with sanitized values (same approach as template API)
    const updatedVarsList = currentVarsList.map((variable: any) => {
      if (sanitizedOverrides.hasOwnProperty(variable.name)) {
        return {
          ...variable,
          data: sanitizedOverrides[variable.name]
        };
      }
      return variable;
    });

    // Add new variables that don't exist yet
    for (const [variableName, sanitizedValue] of Object.entries(sanitizedOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (!existingVariable) {
        // Create new variable with minimal structure
        updatedVarsList.push({
          name: variableName,
          data: sanitizedValue,
          links: deriveLinks(variableName, templateVars, currentVarsList),
          gui: deriveGuiConfig(variableName, templateVars, currentVarsList),
          component_name: deriveComponentName(variableName, templateVars, currentVarsList),
        });
      }
    }

    // Create the updated overrides structure
    const updatedOverrides = {
      ...currentOverrides,
      vars: updatedVarsList
    };

    // Update the dataset with new variable overrides
    const { data: updatedDataset, error: updateError } = await supabase
      .from('datasets')
      .update({
        variable_overrides: updatedOverrides,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('id, name, variable_overrides')
      .single();

    if (updateError) {
      console.error('Error updating dataset variables:', updateError);
      return NextResponse.json(
        { error: 'Failed to update dataset variables' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedDataset.id,
        name: updatedDataset.name,
        updatedVariables: Object.keys(sanitizedOverrides),
        totalOverrides: updatedVarsList.length,
        variable_overrides: updatedOverrides
      }
    });

  } catch (error) {
    console.error('Dataset variable update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const DELETE = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const url = new URL(request.url);
    const variableName = url.searchParams.get('name');

    if (!id) {
      return NextResponse.json(
        { error: 'Dataset ID is required' },
        { status: 400 }
      );
    }

    if (!variableName) {
      return NextResponse.json(
        { error: 'Variable name is required as query parameter' },
        { status: 400 }
      );
    }

    // First, get the current dataset to verify ownership and get current overrides
    const { data: currentDataset, error: fetchError } = await supabase
      .from('datasets')
      .select('variable_overrides, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching dataset:', fetchError);
      return NextResponse.json(
        { error: 'Dataset not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentDataset.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current variable overrides structure
    const currentOverrides = currentDataset.variable_overrides || { vars: [] };
    const currentVarsList = Array.isArray(currentOverrides.vars) ? currentOverrides.vars : [];

    // Check if variable exists
    const variableExists = currentVarsList.some((v: any) => v.name === variableName);
    if (!variableExists) {
      return NextResponse.json(
        { error: 'Variable override not found' },
        { status: 404 }
      );
    }

    // Remove the variable from the list
    const updatedVarsList = currentVarsList.filter((v: any) => v.name !== variableName);

    // Create the updated overrides structure
    const updatedOverrides = {
      ...currentOverrides,
      vars: updatedVarsList
    };

    // Update the dataset with new variable overrides
    const { data: updatedDataset, error: updateError } = await supabase
      .from('datasets')
      .update({
        variable_overrides: updatedOverrides
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('id, name, variable_overrides')
      .single();

    if (updateError) {
      console.error('Error removing dataset variable override:', updateError);
      return NextResponse.json(
        { error: 'Failed to remove dataset variable override' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedDataset.id,
        name: updatedDataset.name,
        removedVariable: variableName,
        totalOverrides: updatedVarsList.length,
        variable_overrides: updatedOverrides
      }
    });

  } catch (error) {
    console.error('Dataset variable removal error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
