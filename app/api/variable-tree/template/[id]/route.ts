import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withAuth } from '@/lib/api/withAuth';
import {
  sanitizeVariableOverrideMap,
  deriveGuiConfig,
  deriveLinks,
  deriveComponentName,
} from '@/lib/utils/variableOverrides';

/**
 * PUT /api/variable-tree/template/[id]
 * Updates template variables in the Variable Tree system
 *
 * Body: { variableOverrides: { [variableName: string]: any } }
 *
 * This endpoint handles variable updates for the Variable Tree system,
 * which stores variables differently from the legacy template system.
 * Template variables are stored in global_job_templates.vars field.
 */

/**
 * DELETE /api/variable-tree/template/[id]?name=VARIABLE_NAME
 * Removes a template variable override (resets to inherited/default)
 *
 * Query params: name - the variable name to remove
 *
 * This endpoint removes a variable from the template's vars array,
 * effectively resetting it to its default state or removing it entirely.
 */
export const PUT = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const { variableOverrides } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (!variableOverrides || typeof variableOverrides !== 'object') {
      return NextResponse.json(
        { error: 'variableOverrides is required and must be an object' },
        { status: 400 }
      );
    }

    // First, get the current template to verify ownership and get current vars
    const { data: currentTemplate, error: fetchError } = await supabase
      .from('global_job_templates')
      .select('vars, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching template:', fetchError);
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentTemplate.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current vars structure
    const currentVars = currentTemplate.vars || { vars: [] };
    const currentVarsList = Array.isArray(currentVars.vars) ? currentVars.vars : [];

    // Validate level constraints for each variable being updated
    const templateLevel = 0; // Template is level 0 (Global)
    for (const [variableName, variableValue] of Object.entries(variableOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (existingVariable && existingVariable.gui?.constraints) {
        const constraints = existingVariable.gui.constraints;
        const minLevel = constraints.minLevel ?? 0;
        const maxLevel = constraints.maxLevel ?? 999;

        if (templateLevel < minLevel || templateLevel > maxLevel) {
          return NextResponse.json(
            {
              error: `Variable '${variableName}' cannot be edited at template level (${templateLevel}). Allowed levels: ${minLevel}-${maxLevel}`
            },
            { status: 400 }
          );
        }
      }
    }

    const sanitizedOverrides = sanitizeVariableOverrideMap(variableOverrides, {
      currentVars: currentVarsList,
    });

    // Update variables with new values
    const updatedVarsList = currentVarsList.map((variable: any) => {
      if (sanitizedOverrides.hasOwnProperty(variable.name)) {
        return {
          ...variable,
          data: sanitizedOverrides[variable.name]
        };
      }
      return variable;
    });

    // Add new variables that don't exist yet
    for (const [variableName, sanitizedValue] of Object.entries(sanitizedOverrides)) {
      const existingVariable = currentVarsList.find((v: any) => v.name === variableName);
      if (!existingVariable) {
        // Create new variable with minimal structure
        updatedVarsList.push({
          name: variableName,
          data: sanitizedValue,
          links: deriveLinks(variableName, [], currentVarsList),
          gui: deriveGuiConfig(variableName, [], currentVarsList),
          component_name: deriveComponentName(variableName, [], currentVarsList),
        });
      }
    }

    // Update the template with new vars
    const { data: updatedTemplate, error: updateError } = await supabase
      .from('global_job_templates')
      .update({
        vars: { vars: updatedVarsList }
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error updating template variables:', updateError);
      return NextResponse.json(
        { error: 'Failed to update template variables' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedTemplate.id,
        updatedVariables: Object.keys(sanitizedOverrides),
        totalVariables: updatedVarsList.length
      }
    });

  } catch (error) {
    console.error('Template variable update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const DELETE = withAuth(async (userId, request, { params }) => {
  try {
    const supabase = createClient();
    const { id } = params;
    const url = new URL(request.url);
    const variableName = url.searchParams.get('name');

    if (!id) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (!variableName) {
      return NextResponse.json(
        { error: 'Variable name is required as query parameter' },
        { status: 400 }
      );
    }

    // First, get the current template to verify ownership and get current vars
    const { data: currentTemplate, error: fetchError } = await supabase
      .from('global_job_templates')
      .select('vars, user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching template:', fetchError);
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (currentTemplate.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get current vars structure
    const currentVars = currentTemplate.vars || { vars: [] };
    const currentVarsList = Array.isArray(currentVars.vars) ? currentVars.vars : [];

    // Check if variable exists
    const variableExists = currentVarsList.some((v: any) => v.name === variableName);
    if (!variableExists) {
      return NextResponse.json(
        { error: 'Variable not found' },
        { status: 404 }
      );
    }

    // Remove the variable from the list
    const updatedVarsList = currentVarsList.filter((v: any) => v.name !== variableName);

    // Update the template with new vars
    const { data: updatedTemplate, error: updateError } = await supabase
      .from('global_job_templates')
      .update({
        vars: { vars: updatedVarsList }
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error removing template variable:', updateError);
      return NextResponse.json(
        { error: 'Failed to remove template variable' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedTemplate.id,
        removedVariable: variableName,
        totalVariables: updatedVarsList.length
      }
    });

  } catch (error) {
    console.error('Template variable removal error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
