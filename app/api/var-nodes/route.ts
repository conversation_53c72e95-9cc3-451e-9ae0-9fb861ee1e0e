import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');
    const datasetId = searchParams.get('datasetId');
    const templateId = searchParams.get('templateId');

    if (!key) {
        return NextResponse.json({ error: 'Key parameter is required' }, { status: 400 });
    }

    try {
        // Use the new filtered function that only returns relevant nodes
        const { data: results, error } = await supabase
            .rpc('get_var_nodes_filtered', {
                p_key: key,
                p_user_id: userId,
                p_dataset_id: datasetId ? parseInt(datasetId) : null,
                p_template_id: templateId ? parseInt(templateId) : null
            });

        if (error) {
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Sort by path, but keep active items first within each kind
        const sortedResults = (results || []).sort((a: any, b: any) => {
            // First sort by active status (active items first)
            if (a.is_active !== b.is_active) {
                return b.is_active ? 1 : -1;
            }
            // Then sort by kind (categories, datasets, templates)
            const kindOrder = { 'cat': 1, 'ds': 2, 'tpl': 3 };
            if (kindOrder[a.kind as keyof typeof kindOrder] !== kindOrder[b.kind as keyof typeof kindOrder]) {
                return kindOrder[a.kind as keyof typeof kindOrder] - kindOrder[b.kind as keyof typeof kindOrder];
            }
            // Finally sort by path
            return a.path.localeCompare(b.path);
        });

        return NextResponse.json({ success: true, data: sortedResults });

    } catch (error) {
        console.error('Var-nodes endpoint error:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
