import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
import JSZip from 'jszip';

export const runtime = 'nodejs';

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const taskId = Array.isArray(params.id) ? params.id[0] : params.id; // Renamed jobId to taskId

    if (!taskId) { // Renamed jobId to taskId
        return NextResponse.json({ error: 'Task ID is required' }, { status: 400 }); // Updated error message
    }

    // Verify that the task belongs to the user
    const { data: task, error: taskError } = await supabase // Renamed job to task, jobError to taskError
        .from('tasks') // Renamed jobs to tasks
        .select('id')
        .eq('id', taskId) // Renamed jobId to taskId
        .eq('user_id', userId)
        .single();

    if (taskError || !task) { // Renamed jobError to taskError, job to task
        console.error('Task access error:', taskError); // Updated error message
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get all visible results for the job
    const { data: results, error: resultsError } = await supabase
        .from('task_results')
        .select('*')
        .eq('task_id', taskId) // Renamed job_id to task_id, jobId to taskId
        .eq('visible', true);

    if (resultsError) {
        console.error('Error fetching results:', resultsError);
        return NextResponse.json({ error: resultsError.message }, { status: 500 });
    }

    if (!results || results.length === 0) {
        return NextResponse.json({ error: 'No visible results found' }, { status: 404 });
    }

    console.log(`Found ${results.length} visible results for task ${taskId}`); // Renamed job to task, jobId to taskId

    const zip = new JSZip();
    for (const result of results) {
        try {
            // Clean up the storage path
            let storagePath = result.file_path;
            if (storagePath.startsWith('/')) {
                storagePath = storagePath.substring(1);
            }

            const { data, error: downloadError } = await supabase.storage
                .from(process.env.NEXT_PUBLIC_RESULT_BUCKET_NAME || 'job-results')
                .download(storagePath);

            if (downloadError) {
                console.error(`Error downloading file ${result.file_name}:`, downloadError);
                continue;
            }

            let fileData;
            if (typeof data.arrayBuffer === 'function') {
                const arrayBuffer = await data.arrayBuffer();
                fileData = new Uint8Array(arrayBuffer);
            } else {
                fileData = data;
            }

            // Organize files by type
            const typeFolder = result.file_type || 'other';
            zip.file(`${typeFolder}/${result.file_name}`, fileData);

            console.log(`Added ${result.file_name} to zip (${result.file_type})`);
        } catch (error) {
            console.error(`Error processing file ${result.file_name}:`, error);
        }
    }

    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });

    return new NextResponse(zipContent, {
        headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': `attachment; filename="task_${taskId}_results.zip"` // Renamed jobId to taskId
        }
    });
});
