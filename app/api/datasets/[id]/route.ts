import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request, { params }) => {
  const supabase = createClient();
  const { id } = params;

  const { data, error } = await supabase
    .from('datasets')
    .select('*')
    .eq('id', id)
    .eq('user_id', userId)
    .single();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ success: true, data });
});

export const PUT = withAuth(async (userId, request, { params }) => {
  const supabase = createClient();
  const { id } = params;
  const { name, description, categoryId, variableOverrides, filePaths, fileTypes } = await request.json();

  const { data, error } = await supabase.rpc('update_dataset_with_files', {
    p_dataset_id: id,
    p_user_id: userId,
    p_name: name,
    p_description: description,
    p_category_id: categoryId,
    p_variable_overrides: variableOverrides,
    p_file_paths: filePaths,
    p_file_types: fileTypes
  });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ success: true, data });
});

export const DELETE = withAuth(async (userId, request, { params }) => {
  const supabase = createClient();
  const { id } = params;

  const { error } = await supabase
    .from('datasets')
    .delete()
    .eq('id', id)
    .eq('user_id', userId);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ success: true });
});
