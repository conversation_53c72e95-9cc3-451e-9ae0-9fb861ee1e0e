import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);

    const targetType = searchParams.get('target_type');
    const targetId = searchParams.get('target_id');
    const jobOnly = searchParams.get('job_only') === 'true'; // New parameter for job-only tickets

    // Validate required parameters
    if (!targetType || !targetId) {
        return NextResponse.json({ 
            error: 'target_type and target_id are required' 
        }, { status: 400 });
    }

    // Validate target_type
    const validTargetTypes = ['job', 'task', 'dataset'];
    if (!validTargetTypes.includes(targetType)) {
        return NextResponse.json({ 
            error: 'Invalid target_type. Must be one of: job, task, dataset' 
        }, { status: 400 });
    }

    // Validate target_id is a number
    const targetIdNum = parseInt(targetId);
    if (isNaN(targetIdNum)) {
        return NextResponse.json({ 
            error: 'target_id must be a valid number' 
        }, { status: 400 });
    }

    try {
        let tickets, error;

        if (jobOnly && targetType === 'job') {
            // For job-only requests, we need to get all tickets for this job
            // and then filter out those that also have task targets
            const { data: allJobTickets, error: jobError } = await supabase
                .from('tickets')
                .select(`
                    *,
                    ticket_targets(
                        id,
                        target_type,
                        target_id
                    ),
                    ticket_messages(
                        id,
                        body,
                        created_at,
                        author_id
                    ),
                    ticket_status_history(
                        id,
                        old_status,
                        new_status,
                        changed_at,
                        changed_by
                    )
                `)
                .or(`creator_id.eq.${userId},assignee_id.eq.${userId}`)
                .order('created_at', { ascending: false });

            if (jobError) {
                error = jobError;
            } else {
                // Filter tickets that have the requested job target but no task targets
                tickets = allJobTickets?.filter(ticket => {
                    const hasRequestedJobTarget = ticket.ticket_targets?.some((target: any) =>
                        target.target_type === 'job' && target.target_id === targetIdNum
                    );
                    const hasAnyTaskTargets = ticket.ticket_targets?.some((target: any) =>
                        target.target_type === 'task'
                    );
                    // Include only if it has the requested job target but NO task targets
                    return hasRequestedJobTarget && !hasAnyTaskTargets;
                }) || [];
            }
        } else {
            // Regular query for non-job-only requests
            const { data: regularTickets, error: regularError } = await supabase
                .from('tickets')
                .select(`
                    *,
                    ticket_targets!inner(
                        id,
                        target_type,
                        target_id
                    ),
                    ticket_messages(
                        id,
                        body,
                        created_at,
                        author_id
                    ),
                    ticket_status_history(
                        id,
                        old_status,
                        new_status,
                        changed_at,
                        changed_by
                    )
                `)
                .eq('ticket_targets.target_type', targetType)
                .eq('ticket_targets.target_id', targetIdNum)
                .or(`creator_id.eq.${userId},assignee_id.eq.${userId}`)
                .order('created_at', { ascending: false });

            tickets = regularTickets;
            error = regularError;
        }

        if (error) {
            console.error('Supabase query error:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Tickets are already filtered by the query logic above
        const filteredTickets = tickets || [];

        // Fetch related item names for ticket targets
        if (filteredTickets && filteredTickets.length > 0) {
            for (const ticket of filteredTickets) {
                if (ticket.ticket_targets && ticket.ticket_targets.length > 0) {
                    for (const target of ticket.ticket_targets) {
                        if (target.target_type === 'job') {
                            const { data: job } = await supabase
                                .from('jobs')
                                .select('name')
                                .eq('id', target.target_id)
                                .eq('user_id', userId)
                                .single();
                            target.name = job?.name || `Job #${target.target_id}`;
                        } else if (target.target_type === 'task') {
                            const { data: task } = await supabase
                                .from('tasks')
                                .select('name, job_id')
                                .eq('id', target.target_id)
                                .eq('user_id', userId)
                                .single();
                            target.name = task?.name || `Task #${target.target_id}`;
                            target.job_id = task?.job_id;
                        } else if (target.target_type === 'dataset') {
                            const { data: dataset } = await supabase
                                .from('datasets')
                                .select('name')
                                .eq('id', target.target_id)
                                .eq('user_id', userId)
                                .single();
                            target.name = dataset?.name || `Dataset #${target.target_id}`;
                        }
                    }
                }
            }
        }

        return NextResponse.json({
            success: true,
            data: filteredTickets
        });
    } catch (error) {
        console.error('Tickets by target fetch error:', error);
        return NextResponse.json({ 
            error: 'Failed to fetch tickets' 
        }, { status: 500 });
    }
});
