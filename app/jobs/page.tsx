"use client"

import React, { useState, useEffect } from 'react';
import { Grid, CircularProgress, Box } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import Jobs from '@/components/jobs/Jobs';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useJobs } from '@/lib/hooks/useJobs'; // Updated import path and hook name
import { JobFilters } from '@/types/jobs';

export default function JobsPage() {
    const [page, setPage] = useState(1);
    const [error, setError] = useState<string | null>(null);
    const [filters, setFilters] = useState<JobFilters>({});
    const { data: jobsData, error: fetchError, isLoading, refetch } = useJobs(page, 10); // Updated hook name and variable name

    useEffect(() => {
        if (fetchError) {
            setError(fetchError.message || 'Failed to load jobs');
        }
    }, [fetchError]);

    const handlePageChange = (newPage: number) => {
        setPage(newPage);
    };

    const handleFilterChange = (filterName: string, value: any) => {
        if (filterName === 'reset') {
            setFilters({});
            // Reload with reset filters
            refetch();
            return;
        }

        if (value === '') {
            // Remove the filter
            setFilters(prev => {
                const newFilters = { ...prev };
                delete newFilters[filterName as keyof JobFilters];
                return newFilters;
            });
        } else {
            // Add/update the filter
            setFilters(prev => ({
                ...prev,
                [filterName]: value
            }));
        }
        
        // Reset to first page when filters change
        setPage(1);
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <PageContainer>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        {isLoading ? (
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    height: '300px'
                                }}
                            >
                                <CircularProgress />
                            </Box>
                        ) : (
                            <Jobs
                                jobs={jobsData?.data || []} // Updated variable name
                                totalPages={Math.ceil((jobsData?.count || 0) / 10)} // Updated variable name
                                currentPage={page}
                                onPageChange={handlePageChange}
                                filters={filters}
                                onFilterChange={handleFilterChange}
                            />
                        )}
                    </Grid>
                </Grid>
                <ErrorDialog
                    open={!!error}
                    onClose={() => setError(null)}
                    error={error}
                    title="Jobs Error"
                />
            </PageContainer>
        </LocalizationProvider>
    );
}
