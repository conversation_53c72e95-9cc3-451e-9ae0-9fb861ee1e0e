import { TextField } from '@mui/material';

interface BasicInfoProps {
    name: string;
    description: string;
    onNameChange: (value: string) => void;
    onDescriptionChange: (value: string) => void;
}

export function BasicInfo({
    name,
    description,
    onNameChange,
    onDescriptionChange
}: BasicInfoProps) {
    return (
        <>
            <TextField
                label="Dataset Name"
                value={name}
                onChange={(e) => onNameChange(e.target.value)}
                fullWidth
                margin="normal"
                required
                
            />
            <TextField
                label="Description"
                value={description}
                onChange={(e) => onDescriptionChange(e.target.value)}
                fullWidth
                multiline
                rows={4}
                margin="normal"
            />
        </>
    );
}
