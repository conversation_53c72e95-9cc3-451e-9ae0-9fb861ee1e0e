-- Switch to the Testing schema
SET search_path TO Testing;

-- Create the tables
CREATE TABLE GLOBAL_JOB_TEMPLATES (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSONB,
    variables JSONB,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES auth.users(id)
);

CREATE TABLE CATEGORIES (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_category_id INTEGER,
    variable_overrides JSONB,
    FOREIG<PERSON> KEY (user_id) REFERENCES auth.users(id),
    <PERSON>OR<PERSON><PERSON><PERSON> (parent_category_id) REFERENCES CATEGORIES(id)
);

CREATE TABLE DATASETS (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    category_id INTEGER NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    variable_overrides JSON<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON> (user_id) REFERENCES auth.users(id),
    <PERSON>OR<PERSON><PERSON><PERSON> (category_id) REFERENCES CATEGORIES(id)
);

CREATE TABLE FILES (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    bucket_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    content_type VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES auth.users(id),
    FOREIGN KEY (dataset_id) REFERENCES DATASETS(id)
);

CREATE TABLE JOBS (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    global_job_template_id INTEGER NOT NULL,
    dataset_id INTEGER NOT NULL,
    job_json JSONB,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES auth.users(id),
    FOREIGN KEY (global_job_template_id) REFERENCES GLOBAL_JOB_TEMPLATES(id),
    FOREIGN KEY (dataset_id) REFERENCES DATASETS(id)
);

CREATE TABLE CATEGORY_JOB_TEMPLATES (
    category_id INTEGER NOT NULL,
    global_job_template_id INTEGER NOT NULL,
    PRIMARY KEY (category_id, global_job_template_id),
    FOREIGN KEY (category_id) REFERENCES CATEGORIES(id),
    FOREIGN KEY (global_job_template_id) REFERENCES GLOBAL_JOB_TEMPLATES(id)
);

CREATE TABLE dataset_files (
    dataset_id INTEGER REFERENCES DATASETS(id) ON DELETE CASCADE,
    file_id INTEGER REFERENCES FILES(id),
    file_type TEXT,
    PRIMARY KEY (dataset_id, file_id)
);

-- Enable RLS for each table
ALTER TABLE GLOBAL_JOB_TEMPLATES ENABLE ROW LEVEL SECURITY;
ALTER TABLE CATEGORIES ENABLE ROW LEVEL SECURITY;
ALTER TABLE DATASETS ENABLE ROW LEVEL SECURITY;
ALTER TABLE FILES ENABLE ROW LEVEL SECURITY;
ALTER TABLE JOBS ENABLE ROW LEVEL SECURITY;
ALTER TABLE CATEGORY_JOB_TEMPLATES ENABLE ROW LEVEL SECURITY;
ALTER TABLE dataset_files ENABLE ROW LEVEL SECURITY;

-- Create policies for each table
CREATE POLICY global_job_templates_policy ON GLOBAL_JOB_TEMPLATES
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY categories_policy ON CATEGORIES
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY datasets_policy ON DATASETS
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY files_policy ON FILES
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY jobs_policy ON JOBS
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY category_job_templates_policy ON CATEGORY_JOB_TEMPLATES
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM CATEGORIES
      WHERE CATEGORIES.id = CATEGORY_JOB_TEMPLATES.category_id
      AND CATEGORIES.user_id = auth.uid()
    )
  );

CREATE POLICY dataset_files_access_policy ON dataset_files
FOR ALL
USING (
  EXISTS (
    SELECT 1
    FROM DATASETS
    JOIN auth.users ON DATASETS.user_id = auth.users.id
    WHERE DATASETS.id = dataset_files.dataset_id
      AND auth.users.email = current_user
  )
);
/*
-- Create trigger function
SET search_path TO testing;
CREATE OR REPLACE FUNCTION sync_dataset_files() RETURNS TRIGGER AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
  SET search_path TO testing;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'dataset', NEW.id);
    END LOOP;

    -- Delete old entries
    DELETE FROM dataset_files WHERE dataset_id = NEW.id;
  
    -- Insert new entries
    INSERT INTO dataset_files (dataset_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER sync_dataset_files_trigger
AFTER INSERT OR UPDATE ON DATASETS
FOR EACH ROW EXECUTE FUNCTION sync_dataset_files();

-- Revoke direct access to dataset_files
REVOKE INSERT, UPDATE, DELETE ON dataset_files FROM public;
*/
_____

-- Wechsle zum testing Schema
SET search_path TO testing;

-- Neue Tabellen für die Verknüpfungen
CREATE TABLE category_files (
    category_id INTEGER REFERENCES CATEGORIES(id) ON DELETE CASCADE,
    file_id INTEGER REFERENCES FILES(id),
    file_type TEXT,
    PRIMARY KEY (category_id, file_id)
);

CREATE TABLE job_template_files (
    job_template_id INTEGER REFERENCES GLOBAL_JOB_TEMPLATES(id) ON DELETE CASCADE,
    file_id INTEGER REFERENCES FILES(id),
    file_type TEXT,
    PRIMARY KEY (job_template_id, file_id)
);

-- RLS für die neuen Tabellen aktivieren
ALTER TABLE category_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_template_files ENABLE ROW LEVEL SECURITY;

-- Policies für die neuen Tabellen
CREATE POLICY category_files_access_policy ON category_files
FOR ALL
USING (
  EXISTS (
    SELECT 1
    FROM CATEGORIES
    WHERE CATEGORIES.id = category_files.category_id
      AND CATEGORIES.user_id = auth.uid()
  )
);

CREATE POLICY job_template_files_access_policy ON job_template_files
FOR ALL
USING (
  EXISTS (
    SELECT 1
    FROM GLOBAL_JOB_TEMPLATES
    WHERE GLOBAL_JOB_TEMPLATES.id = job_template_files.job_template_id
      AND GLOBAL_JOB_TEMPLATES.user_id = auth.uid()
  )
);

-- Trigger-Funktionen für Categories und Job Templates
SET search_path TO testing;
CREATE OR REPLACE FUNCTION sync_category_files() RETURNS TRIGGER AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
    SET search_path TO testing;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'category', NEW.id);
    END LOOP;

    DELETE FROM testing.category_files WHERE category_id = NEW.id;
  
    INSERT INTO testing.category_files (category_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;



---

CREATE OR REPLACE FUNCTION sync_job_template_files() RETURNS TRIGGER AS $$
DECLARE
    file_id INTEGER;
BEGIN
    SET search_path TO testing;

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM testing.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO testing.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger für Categories und Job Templates
CREATE TRIGGER sync_category_files_trigger
AFTER INSERT OR UPDATE ON CATEGORIES
FOR EACH ROW EXECUTE FUNCTION sync_category_files();

CREATE TRIGGER sync_job_template_files_trigger
AFTER INSERT OR UPDATE ON GLOBAL_JOB_TEMPLATES
FOR EACH ROW EXECUTE FUNCTION sync_job_template_files();

-- Direkten Zugriff auf die neuen Tabellen einschränken
REVOKE INSERT, UPDATE, DELETE ON category_files FROM public;
REVOKE INSERT, UPDATE, DELETE ON job_template_files FROM public;

----

SET search_path TO testing;
DROP FUNCTION verify_file_ownership;
CREATE OR REPLACE FUNCTION verify_file_ownership(
    p_file_id INTEGER,
    p_object_type TEXT,
    p_object_id INTEGER
) RETURNS VOID AS $$
DECLARE
    v_file_owner uuid;
    v_object_owner uuid;
BEGIN
    SET search_path TO testing;
    -- Get the owner of the file
    SELECT user_id INTO v_file_owner
    FROM files
    WHERE id = p_file_id;

    -- Get the owner of the object (dataset, category, or job_template)
    CASE p_object_type
        WHEN 'dataset' THEN
            SELECT user_id INTO v_object_owner
            FROM datasets
            WHERE id = p_object_id;
        WHEN 'category' THEN
            SELECT user_id INTO v_object_owner
            FROM categories
            WHERE id = p_object_id;
        WHEN 'job_template' THEN
            SELECT user_id INTO v_object_owner
            FROM global_job_templates
            WHERE id = p_object_id;
    END CASE;
    -- Compare the owners
    
    IF v_file_owner != v_object_owner THEN
        RAISE EXCEPTION 'File (ID: %) does not belong to the owner of the % (ID: %)', 
                        p_file_id, p_object_type, p_object_id;
    END IF;
    
END;
$$ LANGUAGE plpgsql;

--- create batch table


CREATE TABLE BATCHES (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES auth.users(id)
);

-- Add batch_id to JOBS table
ALTER TABLE JOBS ADD COLUMN batch_id INTEGER REFERENCES BATCHES(id);

-- Enable RLS for BATCHES table
ALTER TABLE BATCHES ENABLE ROW LEVEL SECURITY;

-- Create policy for BATCHES table
CREATE POLICY batches_policy ON BATCHES
  FOR ALL USING (auth.uid() = user_id);

  -- Gewähre Lesezugriff für authentifizierte Benutzer
GRANT SELECT ON batches TO authenticated;

-- Gewähre Schreibzugriff (INSERT) für authentifizierte Benutzer
GRANT INSERT ON batches TO authenticated;

-- Wenn Sie auch UPDATE und DELETE benötigen:
GRANT UPDATE, DELETE ON batches TO authenticated;

-- Wenn Sie die auto-increment ID-Sequenz verwenden, gewähren Sie auch Zugriff darauf:
GRANT USAGE, SELECT ON SEQUENCE batches_id_seq TO authenticated;

---- Trigger for file storage changes
SET search_path TO testing;

CREATE OR REPLACE FUNCTION handle_storage_change()
RETURNS TRIGGER AS $$
DECLARE
  file_owner_id uuid;
  file_name text;
BEGIN
  file_owner_id := (NEW.owner_id)::uuid;
  file_name := split_part(NEW.name, '/', -1);

  IF (TG_OP = 'INSERT') THEN
    INSERT INTO testing.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
    VALUES (
      file_owner_id,
      TG_ARGV[0]::text,
      NEW.name,
      file_name,
      COALESCE((NEW.metadata->>'size')::bigint, 0),
      COALESCE(NEW.metadata->>'mimetype', 'application/octet-stream')
    );
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM testing.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SET search_path TO testing;

CREATE OR REPLACE FUNCTION handle_storage_change()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'INSERT') THEN
    INSERT INTO testing.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
    VALUES (auth.uid(), TG_ARGV[0]::text, NEW.name, NEW.name, (NEW.metadata->>'size')::bigint, NEW.metadata->>'mimetype');
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM testing.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

--- rpc for creating datasets

CREATE OR REPLACE FUNCTION testing.create_dataset_with_files(
    p_user_id uuid,
    p_name text,
    p_description text,
    p_category_id integer,
    p_variable_overrides jsonb,
    p_file_paths text[],
    p_file_types text[]
) RETURNS json AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO testing.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOR i IN 1..v_files_count LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id, file_type)
            VALUES (v_dataset_id, v_file_id, v_file_type);
            v_associated_files_count := v_associated_files_count + 1;
        END IF;
    END LOOP;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
----
CREATE OR REPLACE FUNCTION testing.update_dataset_with_files(
    p_dataset_id integer,
    p_user_id uuid,
    p_name text,
    p_description text,
    p_category_id integer,
    p_variable_overrides jsonb,
    p_file_paths text[],
    p_file_types text[]
) RETURNS json AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM testing.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE testing.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM testing.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

--- cascade delete on files table
SET search_path TO testing;
ALTER TABLE dataset_files
DROP CONSTRAINT dataset_files_file_id_fkey,
ADD CONSTRAINT dataset_files_file_id_fkey
FOREIGN KEY (file_id) REFERENCES FILES(id)
ON DELETE CASCADE;
-- policy for dataset_files
SET search_path TO testing;
CREATE POLICY dataset_files_access_policy ON dataset_files
FOR ALL
USING (
  EXISTS (
    SELECT 1
    FROM datasets d
    JOIN files f ON f.id = dataset_files.file_id
    WHERE d.id = dataset_files.dataset_id
      AND d.user_id = auth.uid()
      AND f.user_id = auth.uid()
  )
);