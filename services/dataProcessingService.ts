import { createClient } from "@/utils/supabase/client";

interface Variable {
  data: string;
  name: string;
  links: any[];
}

interface VariableContainer {
  vars: Variable[];
}

interface Template {
  id: string;
  name: string;
}

interface Dataset {
  id: string;
  name: string;
}

const supabase = createClient();

const mergeVariables = (baseVars: VariableContainer, overrideVars: VariableContainer): VariableContainer => {
  if (!overrideVars?.vars) return baseVars;
  if (!baseVars?.vars) return overrideVars;

  const mergedVarsMap = new Map(
    baseVars.vars.map(v => [v.name, v])
  );

  overrideVars.vars.forEach(overrideVar => {
    mergedVarsMap.set(overrideVar.name, overrideVar);
  });

  return {
    vars: Array.from(mergedVarsMap.values())
  };
};

export async function processData(selectedTemplates: Template[], selectedDatasets: Dataset[], customJobName?: string) {
    const startTime = performance.now();
    console.log(`🚀 Starting job creation with ${selectedTemplates.length} templates and ${selectedDatasets.length} datasets`);

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) { // Add check for null user
      console.error('Error fetching user or user is null:', userError);
      return; // Exit if no user
    }

    const userId = user.id; // Now safe to access user.id
    const tasks = [];

    // Step 1: Create Job
    const jobStartTime = performance.now();
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .insert({
        user_id: userId,
        name: customJobName || `Job ${new Date().toISOString()}`,
        description: `Job created for ${selectedTemplates.length} templates and ${selectedDatasets.length} datasets`,
        status: 'queued'
      })
      .select()
      .single();

    if (jobError) {
      console.error('Error creating job:', jobError);
      return;
    }

    const jobEndTime = performance.now();
    console.log(`✅ Job created in ${(jobEndTime - jobStartTime).toFixed(2)}ms (Job ID: ${job.id})`);

    // Step 2-4: Parallel fetch all data (MAJOR OPTIMIZATION)
    const parallelFetchStartTime = performance.now();
    const templateIds = selectedTemplates.map(t => t.id);
    const datasetIds = selectedDatasets.map(d => d.id);

    console.log(`🚀 Parallel fetching: ${templateIds.length} templates, ${datasetIds.length} datasets, and files...`);

    // Execute all queries in parallel
    const [templatesResult, datasetsResult, filesResult] = await Promise.all([
      supabase
        .from('global_job_templates')
        .select('id, template_data, vars')
        .in('id', templateIds),

      supabase
        .from('datasets')
        .select('id, variable_overrides')
        .in('id', datasetIds),

      supabase
        .from('dataset_files')
        .select('dataset_id, file_id, file_type')
        .in('dataset_id', datasetIds)
    ]);

    // Check for errors
    if (templatesResult.error) {
      console.error('Error fetching templates data:', templatesResult.error);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    if (datasetsResult.error) {
      console.error('Error fetching datasets data:', datasetsResult.error);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    if (filesResult.error) {
      console.error('Error fetching dataset files:', filesResult.error);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    // Create maps for quick lookup
    const templateDataMap = new Map(templatesResult.data.map(t => [t.id, t]));
    const datasetDataMap = new Map(datasetsResult.data.map(d => [d.id, d]));

    // Group dataset files by dataset_id for quick lookup
    const datasetFilesMap = new Map<string, any[]>();
    filesResult.data.forEach(file => {
      const datasetId = file.dataset_id.toString();
      if (!datasetFilesMap.has(datasetId)) {
        datasetFilesMap.set(datasetId, []);
      }
      datasetFilesMap.get(datasetId)!.push(file);
    });

    const parallelFetchEndTime = performance.now();
    console.log(`✅ Parallel fetch completed in ${(parallelFetchEndTime - parallelFetchStartTime).toFixed(2)}ms`);
    console.log(`📊 Fetched: ${templatesResult.data.length} templates, ${datasetsResult.data.length} datasets, ${filesResult.data.length} files`);

    // Step 5: Process all combinations in memory (much faster)
    const processingStartTime = performance.now();
    const totalCombinations = selectedTemplates.length * selectedDatasets.length;
    console.log(`⚙️ Processing ${totalCombinations} template-dataset combinations...`);

    for (const template of selectedTemplates) {
      const templateData = templateDataMap.get(template.id);
      if (!templateData) {
        console.error(`Template data not found for template ${template.id}`);
        continue;
      }

      for (const dataset of selectedDatasets) {
        const datasetData = datasetDataMap.get(dataset.id);
        if (!datasetData) {
          console.error(`Dataset data not found for dataset ${dataset.id}`);
          continue;
        }

        const datasetFiles = datasetFilesMap.get(dataset.id.toString()) || [];

        // Merge variables with priority: template < category (future) < dataset
        let finalVars = templateData.vars as VariableContainer;

        // Here you can add category vars merging when implemented
        // finalVars = mergeVariables(finalVars, categoryVars);

        // Merge dataset overrides
        finalVars = mergeVariables(finalVars, datasetData.variable_overrides);

        // Ensure finalVars and finalVars.vars exist
        if (!finalVars) {
          finalVars = { vars: [] };
        }
        if (!finalVars.vars) {
          finalVars.vars = [];
        }

        // Add or update DSNAME variable using dataset.name
        const dsnameVarIndex = finalVars.vars.findIndex(v => v.name === 'DSNAME');
        if (dsnameVarIndex > -1) {
          // Update existing DSNAME
          finalVars.vars[dsnameVarIndex].data = dataset.name;
        } else {
          // Add new DSNAME
          finalVars.vars.push({ name: 'DSNAME', data: dataset.name, links: [] });
        }

        // Define the type for the accumulator explicitly
        const fileTypeMap = datasetFiles.reduce((acc: Record<string, string[]>, file) => {
          const fileType = file.file_type as string; // Assert file_type as string if needed, or handle null/undefined
          const fileId = file.file_id as string;   // Assert file_id as string if needed

          if (!acc[fileType]) {
            acc[fileType] = [];
          }
          acc[fileType].push(fileId);
          return acc;
        }, {} as Record<string, string[]>); // Initialize with the correct type

        const workervars = Object.entries(fileTypeMap).map(([fileType, fileIds]) => ({
          name: fileType,
          file_ids: fileIds
        }));

        tasks.push({
          user_id: userId,
          name: `${template.name} - ${dataset.name}`,
          description: `Task created from template ${template.name} for dataset ${dataset.name}`,
          global_job_template_id: template.id,
          dataset_id: dataset.id,
          job_json: templateData.template_data,
          vars: finalVars,
          workervars: workervars,
          job_id: job.id,
          status: 'queued'
        });
      }
    }

    const processingEndTime = performance.now();
    console.log(`✅ Task data processed in ${(processingEndTime - processingStartTime).toFixed(2)}ms (${tasks.length} tasks created)`);

    // Step 6: Batch insert for all tasks (with chunking for large datasets)
    const insertStartTime = performance.now();

    // Calculate payload size for diagnostics
    const payloadSize = JSON.stringify(tasks).length;
    const avgTaskSize = Math.round(payloadSize / tasks.length);
    console.log(`💾 Inserting ${tasks.length} tasks into database...`);
    console.log(`📦 Payload size: ${(payloadSize / 1024).toFixed(1)}KB total, ${(avgTaskSize / 1024).toFixed(1)}KB per task`);

    // Dynamic batch size based on payload size
    const SMALL_BATCH_SIZE = 10;  // For very large payloads
    const MEDIUM_BATCH_SIZE = 50; // For medium payloads
    const LARGE_BATCH_SIZE = 200; // For small payloads

    let BATCH_SIZE;
    if (avgTaskSize > 50 * 1024) { // > 50KB per task
      BATCH_SIZE = SMALL_BATCH_SIZE;
      console.log(`🐌 Large tasks detected (${(avgTaskSize/1024).toFixed(1)}KB each), using small batch size: ${BATCH_SIZE}`);
    } else if (avgTaskSize > 10 * 1024) { // > 10KB per task
      BATCH_SIZE = MEDIUM_BATCH_SIZE;
      console.log(`🚶 Medium tasks detected (${(avgTaskSize/1024).toFixed(1)}KB each), using medium batch size: ${BATCH_SIZE}`);
    } else {
      BATCH_SIZE = LARGE_BATCH_SIZE;
      console.log(`🏃 Small tasks detected (${(avgTaskSize/1024).toFixed(1)}KB each), using large batch size: ${BATCH_SIZE}`);
    }
    let insertedCount = 0;

    if (tasks.length <= BATCH_SIZE) {
      // Single insert for smaller batches
      console.log(`🔄 Single insert for ${tasks.length} tasks...`);
      const singleInsertStart = performance.now();

      // Use a timeout to detect slow database operations
      const insertPromise = supabase.from('tasks').insert(tasks);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database insert timeout after 30 seconds')), 30000)
      );

      const { data, error } = await Promise.race([insertPromise, timeoutPromise]) as any;

      const singleInsertEnd = performance.now();
      console.log(`⏱️ Database round-trip took ${(singleInsertEnd - singleInsertStart).toFixed(2)}ms`);

      if (error) {
        console.error('Error creating tasks:', error);
        await supabase.from('jobs').delete().eq('id', job.id);
        return;
      }
      insertedCount = tasks.length;
    } else {
      // Parallel chunked inserts for larger batches (MAJOR OPTIMIZATION)
      console.log(`📦 Large batch detected, splitting into chunks of ${BATCH_SIZE} with parallel processing...`);

      const chunks = [];
      for (let i = 0; i < tasks.length; i += BATCH_SIZE) {
        chunks.push(tasks.slice(i, i + BATCH_SIZE));
      }

      console.log(`🚀 Processing ${chunks.length} chunks in parallel...`);

      try {
        const insertPromises = chunks.map((chunk, index) => {
          console.log(`💾 Starting chunk ${index + 1}/${chunks.length} (${chunk.length} tasks)...`);
          return supabase.from('tasks').insert(chunk);
        });

        const results = await Promise.all(insertPromises);

        // Check for errors
        for (let i = 0; i < results.length; i++) {
          if (results[i].error) {
            console.error(`Error creating tasks in chunk ${i + 1}:`, results[i].error);
            await supabase.from('jobs').delete().eq('id', job.id);
            return;
          }
          insertedCount += chunks[i].length;
        }

        console.log(`✅ All ${chunks.length} chunks inserted successfully`);
      } catch (error) {
        console.error('Error in parallel insert:', error);
        await supabase.from('jobs').delete().eq('id', job.id);
        return;
      }
    }

    const insertEndTime = performance.now();
    console.log(`✅ ${insertedCount} tasks inserted in ${(insertEndTime - insertStartTime).toFixed(2)}ms`);

    const totalEndTime = performance.now();
    console.log(`🎉 Job creation completed in ${(totalEndTime - startTime).toFixed(2)}ms total`);
    console.log(`📊 Performance breakdown:
    - Job creation: ${(jobEndTime - jobStartTime).toFixed(2)}ms
    - Parallel data fetch: ${(parallelFetchEndTime - parallelFetchStartTime).toFixed(2)}ms
    - Processing: ${(processingEndTime - processingStartTime).toFixed(2)}ms
    - Database insert: ${(insertEndTime - insertStartTime).toFixed(2)}ms`);

    return { tasks: tasks, job: job };
}
