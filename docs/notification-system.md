# Notification System Documentation

## Overview

The notification system provides real-time updates for support ticket status changes. Users receive notifications when their tickets are resolved or require their attention (waiting_on_customer status).

## Features

- **Real-time Updates**: Uses Supabase Realtime for instant notifications
- **Badge Counter**: Shows unread notification count in header
- **Professional UI**: Material UI dropdown with scrollable list
- **Direct Navigation**: Click notifications to jump to specific tickets
- **Auto-mark as Seen**: Notifications marked as read when bell is clicked

## Architecture

### Database Schema

#### `user_notification_states`
- `user_id` (uuid, primary key) - References auth.users
- `last_seen_at` (timestamptz) - When user last opened notifications
- `created_at` (timestamptz) - Record creation time
- `updated_at` (timestamptz) - Last update time

### API Endpoints

#### `GET /api/notifications`
- Returns notifications for current user
- Filters tickets with status 'resolved' or 'waiting_on_customer'
- Only shows tickets updated since last_seen_at
- Includes unread count

#### `PATCH /api/notifications/mark-seen`
- Updates user's last_seen_at timestamp
- Resets unread count to 0

### Components

#### `NotificationBell`
- Main notification icon in header
- Shows badge with unread count
- Handles dropdown open/close
- Includes loading and error states

#### `NotificationDropdown`
- Popover content with notification list
- Scrollable list (max 10 items shown)
- "View All Tickets" button
- Empty state messaging

#### `NotificationItem`
- Individual notification display
- Shows ticket title, status, and time
- Clickable to navigate to ticket

### Hooks

#### `useNotifications`
- Main hook for notification functionality
- Manages Realtime subscription
- Handles dropdown state
- Provides notification data and actions

#### `useNotificationState`
- Manages user's notification state
- Handles last_seen_at updates
- Provides helper functions for unread counting

## Usage

### Basic Implementation

```tsx
import { NotificationBell } from '@/components/notifications';

// In header component
<NotificationBell color="inherit" />
```

### Custom Hook Usage

```tsx
import { useNotifications } from '@/lib/hooks/useNotifications';

function MyComponent() {
  const {
    notifications,
    unreadCount,
    isLoading,
    handleDropdownToggle
  } = useNotifications();
  
  // Use notification data...
}
```

## Realtime Configuration

The system requires Supabase Realtime to be enabled for the `tickets` table:

```sql
-- Enable realtime for tickets table
ALTER PUBLICATION supabase_realtime ADD TABLE public.tickets;
```

## Notification Triggers

Notifications are triggered when:
1. A ticket's status changes to 'resolved'
2. A ticket's status changes to 'waiting_on_customer'
3. The user is the creator of the ticket

## Navigation Integration

Clicking a notification navigates to:
```
/tickets?ticketId={ticketId}
```

The tickets page automatically opens the TicketDetailDrawer for the specified ticket.

## Performance Considerations

- **Efficient Filtering**: Only subscribes to user's own tickets
- **Limited Display**: Shows max 10 notifications in dropdown
- **Debounced Updates**: Prevents UI flicker from rapid updates
- **Optimistic Updates**: Immediate UI feedback for better UX

## Error Handling

- **Graceful Degradation**: Falls back to polling if Realtime fails
- **Loading States**: Shows spinner while loading notifications
- **Error Display**: Clear error messages for failed operations
- **Retry Logic**: Automatic retry for failed API calls

## Testing

To test the notification system:

1. Create a support ticket
2. Have support staff change status to 'resolved' or 'waiting_on_customer'
3. Verify notification appears in real-time
4. Check badge count updates
5. Test navigation to ticket details

## Future Enhancements

- Email notifications for important updates
- Push notifications for mobile devices
- Notification preferences and filtering
- Bulk mark as read functionality
- Notification history and archiving
