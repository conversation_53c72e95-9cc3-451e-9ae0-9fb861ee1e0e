# Var-Nodes API Verbesserung

## Übersicht

Die `/api/var-nodes` Route wurde verbessert, um nur relevante Knoten für eine spezifische Variable zurückzugeben, anstatt alle Knoten mit dieser Variable.

## Neue Parameter

- `key` (er<PERSON><PERSON><PERSON>): Der Name der Variable
- `datasetId` (optional): ID des Datasets für Kontext-basierte Filterung
- `templateId` (optional): ID des Templates für Kontext-basierte Filterung

## Verbesserungen

### 1. Gefilterte Ergebnisse
- **Vorher**: Alle Knoten mit der Variable wurden zurückgegeben
- **Jetzt**: Nur relevante Knoten im Kontext des angegebenen Datasets/Templates

### 2. Template-Unterstützung
- **Neu**: `templateId` Parameter hinzugefügt
- **Filterung**: Nur das spezifische Template wird zurückgegeben (falls angegeben)

### 3. Pfad-basierte Filterung
- **Categories**: Nur Categories im Pfad des angegebenen Datasets
- **Datasets**: Nur das spezifische Dataset (falls angegeben)
- **Templates**: Nur das spezifische Template (falls angegeben)

## API-Verwendung

```typescript
// Alle Knoten für eine Variable
GET /api/var-nodes?key=VARIABLE_NAME

// Gefiltert nach Dataset-Kontext
GET /api/var-nodes?key=VARIABLE_NAME&datasetId=123

// Gefiltert nach Dataset und Template
GET /api/var-nodes?key=VARIABLE_NAME&datasetId=123&templateId=456
```

## React Hook

```typescript
import { useVarNodes } from '@/lib/hooks/useJobsHooks';

const { varNodes, loading, error } = useVarNodes(
  'VARIABLE_NAME',
  123, // datasetId
  456  // templateId
);
```

## Datenbank-Änderungen

- **Neue RPC-Funktion**: `get_var_nodes_filtered`
- **Migration**: `20250104140000_improve_var_nodes_api.sql`
- **Berechtigungen**: Für `authenticated` Rolle gewährt

## Antwort-Format

```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "path": "category.subcategory",
      "kind": "cat",
      "is_active": false
    },
    {
      "id": 456,
      "path": "category.subcategory.dataset",
      "kind": "ds", 
      "is_active": true
    },
    {
      "id": 789,
      "path": "Template Name",
      "kind": "tpl",
      "is_active": false
    }
  ]
}
```

## Prioritäten für `is_active`

1. **Dataset** (9000) - Höchste Priorität
2. **Category** (2000 + Level) - Tiefere Categories überschreiben Root-Categories
3. **Template** (1000) - Niedrigste Priorität

Nur der Knoten mit der höchsten Priorität wird als `is_active: true` markiert.