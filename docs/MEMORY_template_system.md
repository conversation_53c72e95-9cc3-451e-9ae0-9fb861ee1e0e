# Template System Architecture and Insights

## Core Components

### Data Flow
1. Templates are stored with basic variable information
2. GUI components are stored separately in the `gui_components` table
3. When rendering a template, the system merges:
   - Template variables from the template
   - GUI component parameters from the gui_components table

### Key Hooks
1. `useGetTemplateVars`: Fetches raw template data
2. `useGetGUIComponents`: Fetches GUI component definitions
3. `useMergedTemplateVars`: Combines template vars with GUI component parameters

### Template Store
- Manages the current state of template variables
- Handles validation errors
- Used by both the template editor and renderer

## Important Implementation Details

### Template Variable Merging
```typescript
// The merging process combines:
{
  ...matchingGuiComponent,      // Base GUI component
  ...templateVar,              // Template-specific overrides
  gui: {
    ...matchingGuiComponent.parameters,  // Default parameters
    ...templateVar.gui,                  // Template-specific GUI settings
    component_id: templateVar.gui.component_id
  }
}
```

### EditTemplateDialog Best Practices
1. Always use `useMergedTemplateVars` instead of raw template variables
2. Initialize store with merged variables when they change
3. Use sticky header for better UX
4. Handle validation before saving

### Common Pitfalls
1. Using raw template variables without merging GUI component parameters
2. Not handling undefined states in the template renderer
3. Race conditions between template loading and dialog rendering

## Architecture Decisions

### Component Separation
1. `EditTemplateWrapper`: Handles template loading
2. `EditTemplateDialog`: Handles UI and interactions
3. `TemplateRenderer`: Renders the merged template variables

### State Management
1. Template variables stored in global store
2. GUI components fetched on demand
3. Validation state managed alongside template variables

## Debug Tips
- Check template.vars in console
- Verify GUI component merging
- Look for undefined parameters
- Monitor validation state changes
