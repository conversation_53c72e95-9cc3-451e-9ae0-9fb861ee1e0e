# Template-System Dokumentation

## Überblick
Das Template-System ermöglicht die dynamische Erstellung und Verwaltung von Benutzeroberflächen durch die Verwendung von konfigurierbaren Komponenten. Es besteht aus zwei Hauptteilen:
1. GUI-Komponenten-Definitionen in der Datenbank
2. Template-Definitionen, die diese Komponenten verwenden

## Datenbankstruktur

### GUI Components Tabelle
```sql
CREATE TABLE gui_components (
    id TEXT PRIMARY KEY,
    description TEXT,
    component_name TEXT NOT NULL,
    parameters JSONB NOT NULL
);
```

- `id`: Eindeutige ID der Komponente (z.B. "standard-integer-slider")
- `description`: Beschreibung der Komponente
- `component_name`: Name der tatsächlichen React-Komponente (z.B. "IntegerSlider")
- `parameters`: JSON-Objekt mit Standardwerten und Konfiguration

Beispiel für einen Eintrag:
```json
{
    "id": "standard-integer-slider",
    "description": "Standard integer slider with default range 0-100",
    "component_name": "IntegerSlider",
    "parameters": {
        "type": "integer",
        "label": "Standard Slider",
        "defaultValue": 50,
        "min": 0,
        "max": 100,
        "step": 1
    }
}
```

## Frontend-Komponenten

### Hauptkomponenten

1. **TemplateRenderer** (`components/template/TemplateRenderer.tsx`)
   - Hauptkomponente für das Rendering eines Templates
   - Verwendet `useMergedTemplateVars` für die Datenverwaltung
   - Rendert die Template-Variablen in Gruppen

2. **InputRenderer** (`components/template/InputRenderer.tsx`)
   - Rendert einzelne Eingabekomponenten
   - Verwendet die `componentMap` für das dynamische Laden von Komponenten
   - Verarbeitet Komponenten-spezifische Props und Events

3. **ListGroupRenderer** (`components/template/ListGroupRenderer.tsx`)
   - Gruppiert zusammengehörige Eingabefelder
   - Rendert InputRenderer für jede Variable in der Gruppe

### State Management

1. **templateStore** (`lib/stores/templateStore.ts`)
   - Zentraler Store für Template-Daten
   - Verwaltet Template-Variablen und Validierungsfehler
   - Hauptfunktionen:
     - `setTemplateVars`: Setzt Template-Variablen
     - `updateVariableData`: Aktualisiert einzelne Variablenwerte
     - `setValidationError`: Setzt Validierungsfehler

### Hooks

1. **useTemplateVars** (`lib/hooks/useTemplateVars.ts`)
   - `useGetTemplateVars`: Lädt Template-Daten
   - `useGetGUIComponents`: Lädt GUI-Komponenten-Definitionen
   - `useMergedTemplateVars`: Verbindet Template-Variablen mit GUI-Komponenten
   - `useSaveTemplateVars`: Speichert Template-Änderungen

## Datenfluss

1. **Laden eines Templates**
   ```mermaid
   graph TD
   A[Template Page] --> B[useGetTemplateVars]
   A --> C[useGetGUIComponents]
   B --> D[useMergedTemplateVars]
   C --> D
   D --> E[TemplateRenderer]
   E --> F[ListGroupRenderer]
   F --> G[InputRenderer]
   ```

2. **Aktualisierung von Werten**
   ```mermaid
   graph TD
   A[InputRenderer] --> B[handleChange]
   B --> C[templateStore.updateVariableData]
   C --> D[Template State]
   ```

## Verwendung von GUI-Komponenten

### 1. Komponente in der Datenbank definieren
```sql
INSERT INTO gui_components (id, description, component_name, parameters) 
VALUES (
    'custom-text-field',
    'Text field with custom placeholder',
    'TextField',
    '{"type": "string", "label": "Custom Input", "placeholder": "Enter text..."}'
);
```

### 2. Komponente im Template verwenden
```json
{
    "name": "example_var",
    "gui": {
        "component_id": "custom-text-field",
        "label": "Override Label"  // Optional: überschreibt den Standard-Label
    }
}
```

## Vererbungshierarchie der Eigenschaften

1. Template-Variable-Eigenschaften überschreiben
2. GUI-Komponenten-Parameter (aus der Datenbank) als Fallback
3. Komponenten-Standardwerte als letzter Fallback

## Best Practices

1. **Komponenten-IDs**
   - Verwenden Sie beschreibende IDs (z.B. "large-text-field" statt "text-field-1")
   - Gruppieren Sie ähnliche Komponenten durch Präfixe (z.B. "slider-standard", "slider-precise")

2. **Parameter**
   - Definieren Sie sinnvolle Standardwerte in der Datenbank
   - Dokumentieren Sie spezielle Parameter in der Komponenten-Beschreibung

3. **Validierung**
   - Nutzen Sie das Validierungssystem des Template-Stores
   - Implementieren Sie komponentenspezifische Validierung in der handleChange-Funktion

## Fehlerbehandlung

1. **Unbekannte Komponenten**
   - InputRenderer zeigt eine Fehlermeldung mit der unbekannten Komponenten-ID
   - Logging für Debugging-Zwecke

2. **Validierungsfehler**
   - Werden im templateStore gespeichert
   - Können pro Variable abgerufen und angezeigt werden

## Erweiterung des Systems

### Neue Komponente hinzufügen

1. React-Komponente erstellen unter `components/inputs/`
2. Komponente zur `componentMap` in InputRenderer hinzufügen
3. Komponente in der `gui_components`-Tabelle registrieren

### Neue Funktionalität hinzufügen

1. Parameter in der JSONB-Struktur definieren
2. InputRenderer um neue Parameter erweitern
3. Komponenten-Props entsprechend aktualisieren
