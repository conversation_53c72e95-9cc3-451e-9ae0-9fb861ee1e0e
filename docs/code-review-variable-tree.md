# Variable Tree Component Code Review

Date: 2025-08-25

## Scope
- Reviewed `components/template/VariableTreeView.tsx` and related components: `VariableInputRenderer.tsx`, `VariableStatusBadge.tsx`, `VariableFocusIndicator.tsx`, `CustomTreeItem.tsx`.
- Reviewed hooks: `lib/hooks/useVariableTree.ts`, `lib/hooks/useVariableTreeState.ts`.
- Reviewed helpers: `lib/utils/changeTracking.ts`, `lib/utils/variableState.ts`, `lib/utils/variableColors.ts`.
- Reviewed shared types in `types/variable.ts`.
- Reviewed API routes: `app/api/variable-tree/route.ts`, `app/api/variable-tree/template/[id]/route.ts`, `app/api/variable-tree/category/[id]/route.ts`, `app/api/variable-tree/dataset/[id]/route.ts`.
- Skimmed `app/variable-tree/page.tsx` and client functions in `lib/services/api.ts` used by the hooks.

## What’s Working Well
- Clear separation of concerns:
  - Data fetching/derivation in `useVariableTree`.
  - Local, batched change management in `useVariableTreeState`.
  - API routes split by resource (template/category/dataset) with consistent contracts.
- Thoughtful UX:
  - `RichTreeView` with custom item slot and per-level scroll memory.
  - Status badges with detailed tooltips and a focus indicator present in tree and inputs.
  - “Go to defining level” navigation and “Reset to inherited” are helpful affordances.
- Hierarchy logic:
  - State resolution gives dataset/category precedence with clear `active/overridden/defined-higher/not-set` semantics.
  - `getInheritedValueForContext` enables instant post-reset previews.
- Server-side:
  - Update/delete endpoints verify ownership and enforce min/max level constraints.
  - Consistent patterns across template/category/dataset routes.

## Key Issues and Risks
- Context-unaware change tracking (critical):
  - `VariableChangeTracker` stores changes keyed by `variableName` only. Edits for the same variable in different contexts (e.g., two datasets) overwrite each other; only the last survives. Also prevents batching multiple context-specific changes.
  - `variableChanges: Map<string, any>` in `useVariableTreeState` has the same issue; unsaved values bleed across contexts.
  - Affects `isVariableChanged`, `isVariableResetPending`, and `getVariableValue` logic throughout the UI.
- Fragile “Go to defining level”:
  - Uses `getVariablesByName(...).find(v => v.is_active)` but `getVariablesByName` normalizes `is_active` to true when absent. This can select a wrong instance. Prefer `getVariableState(variableName).activeVariable` which already computes the globally active instance.
- Duplicate fetching and ineffective invalidation:
  - Page component calls `useVariableTree` to gate render, and `VariableTreeView` calls it again → redundant fetch and potential inconsistency.
  - `useVariableTreeState` invalidates React Query keys, but `useVariableTree` doesn’t use React Query, so invalidation doesn’t affect it (you call `refetch()` manually, but the invalidations are misleading).
- Heavy `any` usage:
  - `VariableTreeView` and `useVariableTree` use `any` extensively despite having `VariableTreeNode` and `VariableWithContext` types. This hides shape issues and increases risk.
- UI timing fragility:
  - Scroll restoration uses multiple `setTimeout` calls. This is brittle under concurrent rendering/varied frame rates. Prefer lifecycle/measurement-driven approaches.
- Weak defaults for status fields:
  - `getVariablesByName` defaults `is_active`/`is_overridden` instead of computing/preserving them. Derived state should be computed consistently.
- API/doc mismatch:
  - Design doc shows RPC expecting `UUID`, while route passes `parseInt(templateId)`. Ensure DB signature matches (either adjust SQL or route/docs).
- Minor quality:
  - Production `console.log` in `VariableTreeView`.
  - Category API doesn’t update `updated_at` like dataset API (consistency).
  - `slotProps.item` type assertion (`as any`).

## Performance Observations
- Several O(n) scans per variable per render (`findNode`, `getAllVariablesForContext`, `getVariablesByName`). Fine for moderate trees, but consider:
  - Precomputing maps: `id -> node`, `node -> Set<varName>`, `varName -> instances`.
  - Memoizing `findNodePath` and derived sets based on `(nodeId, datasetId)`.
  - Rendering optimization: toggle to show only GUI variables helps; consider virtualization if lists grow.

## Correctness Improvements
- Make change tracking context-aware:
  - Use a composite context key: `contextKey = <saveLevel>:<targetIdOrTemplate>:<variableName>` (e.g., `template::foo`, `category:42:foo`, `dataset:17:foo`).
  - Store this key in `VariableChange` and use it as the Map key in `VariableChangeTracker`.
  - Change `variableChanges` to `Map<contextKey, any>` and update `getVariableValue`, `isVariableChanged`, `isVariableResetPending` to accept context (`nodeId`, `nodeType`).
  - `groupChangesByContext` becomes trivial and supports multiple changes for the same name across contexts.
- Fix navigation to defining level:
  - Replace active-instance lookup with `const { activeVariable } = getVariableState(variableName)` and derive the target node/dataset IDs from it.
- Unify fetching and cache invalidation:
  - Option A: Lift `useVariableTree` to the page and pass `data`/`refetch` into `VariableTreeView`; remove `useVariableTree` call from the page.
  - Option B: Convert `useVariableTree` to React Query (`useQuery`) so invalidations in `useVariableTreeState` work as intended; keep a single shared cache.
- Strengthen types:
  - Replace `any` with `VariableTreeResponse`, `VariableTreeNode`, `VariableWithContext`.
  - Type `slotProps.item` for `CustomTreeItem` properly.
  - Stop defaulting `is_active`/`is_overridden` in `getVariablesByName`; compute with existing helpers where needed.
- Robust scroll/DOM behavior:
  - Replace `setTimeout` sequences with `useLayoutEffect` tied to `selectedItems` and ref callbacks; optionally gate with `requestAnimationFrame`. Use a CSS class for highlight flash instead of inline style mutations.

## API and Validation
- Align RPC argument types (UUID vs integer) across docs and implementation.
- Consider sanitizing/validating variable names used in update/delete (defensive coding).
- Update category route to set `updated_at` for parity with dataset.

## Testing Gaps
Add targeted tests (Jest + RTL):
- Hooks:
  - `useVariableTree` state derivation for template-only, category override, dataset override scenarios.
  - `getInheritedValueForContext` correctness.
- Change tracking:
  - Multi-context edits preserved and grouped correctly; reset flows handled.
- Components:
  - Toggle inputs/badges, navigate to defining level, reset to inherited, focus indicator appears in correct nodes.
- API:
  - Success, constraint violations (min/max level), deletion of non-existent overrides, ownership denial.

## Quick Wins
- Remove `console.log` in `VariableTreeView` or guard behind a debug flag.
- Use `getVariableState(variableName).activeVariable` for navigation to defining level.
- Avoid duplicate `useVariableTree` call (either lift or convert to React Query).
- Add `updated_at` to category updates for consistency.
- Tighten types and remove `as any` in `slotProps.item`.

## Medium/Larger Changes
- Convert `useVariableTree` to React Query for proper cache + invalidation integration.
- Redesign change tracking to be context-aware throughout (tracker + UI state + save pipeline).
- Extract tree lookup helpers (node map, path finder, var index) with memoization, share across hooks/components.

## Open Questions
- Should multiple contexts be editable before a single “Save”? Current UI suggests yes; if not, simplify by clearing local changes on context switch or warning upon navigation.
- Will backend ever omit `is_active`/`is_overridden` intentionally? If yes, client should compute; if no, remove client-side defaulting to avoid divergence.

## Next Steps (Offer)
- Implement context-aware change tracking (composite keys) and wire through UI.
- Convert `useVariableTree` to React Query and align invalidations.
- Patch `navigateToDefiningLevel` to use `getVariableState`’s `activeVariable`.
- Add initial unit tests for hooks to prevent regressions.

