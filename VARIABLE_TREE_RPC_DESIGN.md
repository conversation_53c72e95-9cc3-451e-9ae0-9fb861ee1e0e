# Variable Tree RPC Function Design

## Overview
This document outlines the design for the new `get_variable_tree_with_context` RPC function that will efficiently return the complete variable tree structure needed for the frontend component.

## Function Signature
```sql
CREATE OR REPLACE FUNCTION get_variable_tree_with_context(
  p_template_id UUID,
  p_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
```

## Required Data Sources

### Tables to Join
1. **categories** - Tree structure
2. **datasets** - Leaf nodes
3. **template_variables** - Template-level variable definitions
4. **dataset_variables** - Dataset-level variable overrides
5. **category_variables** - Category-level variable overrides (if exists)

### Variable State Logic
For each variable at each node, determine:
- `source_level`: Where the variable is defined ('template', 'category', 'dataset')
- `is_active`: Whether this variable value is actually being used
- `is_overridden`: Whether this variable is overridden by a higher priority level
- `defined_at_level`: The original level where this variable was first defined

## SQL Implementation Strategy

### Step 1: Get Tree Structure
```sql
WITH RECURSIVE tree_structure AS (
  -- Root categories
  SELECT 
    id,
    name,
    parent_id,
    'category' as type,
    0 as level,
    ARRAY[id] as path
  FROM categories 
  WHERE parent_id IS NULL
  
  UNION ALL
  
  -- Recursive categories
  SELECT 
    c.id,
    c.name,
    c.parent_id,
    'category' as type,
    ts.level + 1,
    ts.path || c.id
  FROM categories c
  JOIN tree_structure ts ON c.parent_id = ts.id
  
  UNION ALL
  
  -- Datasets as leaf nodes
  SELECT 
    d.id,
    d.name,
    d.category_id as parent_id,
    'dataset' as type,
    ts.level + 1,
    ts.path || d.id
  FROM datasets d
  JOIN tree_structure ts ON d.category_id = ts.id
)
```

### Step 2: Get Variable Context
```sql
variable_context AS (
  SELECT 
    ts.id as node_id,
    ts.type as node_type,
    ts.path,
    tv.name as variable_name,
    
    -- Determine the effective value and source
    CASE 
      WHEN dv.value IS NOT NULL THEN dv.value
      WHEN cv.value IS NOT NULL THEN cv.value  
      ELSE tv.default_value
    END as effective_value,
    
    -- Determine source level
    CASE 
      WHEN dv.value IS NOT NULL THEN 'dataset'
      WHEN cv.value IS NOT NULL THEN 'category'
      ELSE 'template'
    END as source_level,
    
    -- Determine if active (not overridden)
    CASE 
      WHEN ts.type = 'dataset' AND dv.value IS NOT NULL THEN true
      WHEN ts.type = 'category' AND cv.value IS NOT NULL AND dv.value IS NULL THEN true
      WHEN ts.type = 'category' AND tv.default_value IS NOT NULL AND cv.value IS NULL AND dv.value IS NULL THEN true
      ELSE false
    END as is_active,
    
    -- Determine if overridden
    CASE 
      WHEN ts.type = 'category' AND dv.value IS NOT NULL THEN true
      WHEN ts.type = 'template' AND (cv.value IS NOT NULL OR dv.value IS NOT NULL) THEN true
      ELSE false
    END as is_overridden,
    
    'template' as defined_at_level  -- All variables start at template level
    
  FROM tree_structure ts
  CROSS JOIN template_variables tv
  LEFT JOIN dataset_variables dv ON (ts.type = 'dataset' AND dv.dataset_id = ts.id AND dv.variable_name = tv.name)
  LEFT JOIN category_variables cv ON (cv.category_id = ANY(ts.path) AND cv.variable_name = tv.name)
  WHERE tv.template_id = p_template_id
)
```

### Step 3: Build JSON Structure
```sql
SELECT json_build_object(
  'tree', (
    SELECT json_agg(
      json_build_object(
        'id', ts.id,
        'name', ts.name,
        'type', ts.type,
        'variables', (
          SELECT json_agg(
            json_build_object(
              'name', vc.variable_name,
              'value', vc.effective_value,
              'source_level', vc.source_level,
              'is_active', vc.is_active,
              'is_overridden', vc.is_overridden,
              'defined_at_level', vc.defined_at_level
            )
          )
          FROM variable_context vc
          WHERE vc.node_id = ts.id
        ),
        'children', (
          -- Recursive children building
          -- This part needs careful implementation for proper nesting
        )
      )
    )
    FROM tree_structure ts
    WHERE ts.level = 0  -- Start with root nodes
  )
) as result;
```

## Performance Considerations

### Indexing Requirements
```sql
-- Ensure these indexes exist for optimal performance
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_datasets_category_id ON datasets(category_id);
CREATE INDEX IF NOT EXISTS idx_template_variables_template_id ON template_variables(template_id);
CREATE INDEX IF NOT EXISTS idx_dataset_variables_dataset_variable ON dataset_variables(dataset_id, variable_name);
CREATE INDEX IF NOT EXISTS idx_category_variables_category_variable ON category_variables(category_id, variable_name);
```

### Optimization Strategies
1. **Limit Depth**: Add max recursion depth to prevent infinite loops
2. **Pagination**: Consider limiting tree size and implementing pagination
3. **Caching**: Cache results for frequently accessed templates
4. **Selective Loading**: Option to load only specific branches

## Security Considerations

### Row Level Security (RLS)
```sql
-- Ensure the function respects existing RLS policies
-- The function should only return data the user has access to
SET row_security = on;
```

### User Permissions
- Function should validate that `p_user_id` has access to `p_template_id`
- Should respect existing category and dataset permissions
- Consider adding audit logging for sensitive operations

## Error Handling

```sql
BEGIN
  -- Validate inputs
  IF p_template_id IS NULL OR p_user_id IS NULL THEN
    RAISE EXCEPTION 'Template ID and User ID are required';
  END IF;
  
  -- Check template access
  IF NOT EXISTS (SELECT 1 FROM templates WHERE id = p_template_id) THEN
    RAISE EXCEPTION 'Template not found or access denied';
  END IF;
  
  -- Main query logic here
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error building variable tree: %', SQLERRM;
END;
```

## Testing Strategy

### Test Cases
1. **Empty Tree**: Template with no categories/datasets
2. **Simple Tree**: Single category with one dataset
3. **Complex Tree**: Multiple levels with variable overrides
4. **Large Tree**: Performance testing with many nodes
5. **Permission Tests**: User access restrictions
6. **Variable Inheritance**: Correct priority resolution

### Sample Test Data
```sql
-- Create test template
INSERT INTO templates (id, name) VALUES ('test-template-id', 'Test Template');

-- Create test variables
INSERT INTO template_variables (template_id, name, default_value) VALUES 
('test-template-id', 'var1', 'template_default'),
('test-template-id', 'var2', 'template_default2');

-- Create test tree structure
-- ... (categories and datasets)

-- Create variable overrides
-- ... (dataset_variables, category_variables)
```

## Next Steps

1. **Implement Basic Version**: Start with simple tree structure
2. **Add Variable Logic**: Implement variable inheritance rules
3. **Optimize Performance**: Add indexing and query optimization
4. **Add Security**: Implement proper RLS and permissions
5. **Test Thoroughly**: Create comprehensive test suite
6. **Document API**: Update API documentation

## API Integration

Once the RPC is implemented, the API route should be straightforward:

```typescript
// app/api/variable-tree/route.ts
export async function GET(request: NextRequest) {
  return withAuth(async (userId) => {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');
    
    if (!templateId) {
      return NextResponse.json({ error: 'Template ID required' }, { status: 400 });
    }
    
    const { data, error } = await supabase.rpc('get_variable_tree_with_context', {
      p_template_id: templateId,
      p_user_id: userId
    });
    
    if (error) {
      console.error('Variable tree RPC error:', error);
      return NextResponse.json({ error: 'Failed to fetch variable tree' }, { status: 500 });
    }
    
    return NextResponse.json(data);
  })(request);
}
```