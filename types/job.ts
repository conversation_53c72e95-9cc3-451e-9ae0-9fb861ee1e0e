/**
 * Type definitions for Job and Task related components
 */

export interface TaskResult {
  id: string;
  file_type: string;
  file_name: string;
}

export interface Task {
  id: number;
  status: string;
  dataset?: {
    name: string;
    description?: string;
  };
  created_at: string;
  task_results?: TaskResult[];
  global_job_template?: {
    name: string;
  };
  bulk_job_type?: string | null; // Added to identify and filter bulk tasks
}

// Renamed from Batch to Job
export interface Job {
  id: string;
  name: string;
  status: string;
  created_at: string;
  template_name?: string;
  template_count?: number;
  // Renamed from jobs to tasks, keeping inner structure for now as per constraints
  tasks?: {
    global_job_template?: {
      name: string;
    };
  }[];
}

// Updated to use Job type and renamed prop from batch to job
export interface JobProps {
  job: Job;
  tasks: Task[];
}

export interface StatusDisplay {
  text: string;
  color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  tooltip?: string;
}

export interface SortConfig {
  orderBy: string;
  order: 'asc' | 'desc';
}

export interface SupportRequest {
  taskId: number;
  reason: string;
  additionalInfo: string;
}

export type SupportReasonType = 'job_failed' | 'inaccurate_result' | 'missing_files' | 'other';
