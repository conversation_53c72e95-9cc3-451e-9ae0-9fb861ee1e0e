/**
 * Type definitions for Jobs list related components
 */

import { Dayjs } from 'dayjs';

export interface TaskResult {
  id: string;
  file_type: string;
  file_name: string;
  visible: boolean;
}

export interface JobItem {
  id: string;
  name: string;
  status: string;
  created_at: string;
  jobs: { // Reverted rename: Actual data uses 'jobs'
    id: number;
    dataset?: {
      name: string;
    };
    global_job_template?: {
      name: string;
    };
    task_results?: TaskResult[];
  }[];
}

export interface JobFilters {
  status?: string;
  template?: string;
  dateRange?: {
    from: Date | null;
    to: Date | null;
  };
}

export interface JobsProps {
  jobs: JobItem[];
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  filters?: JobFilters;
  onFilterChange?: (filterName: string, value: any) => void;
}

export interface JobsTableProps {
  jobs: JobItem[];
  orderBy: string;
  order: 'asc' | 'desc';
  onSort: (property: string) => void;
  downloadingFiles: Record<string, boolean>;
  onDownloadFileType: (jobId: string, fileType: string) => void; // Renamed batchId to jobId
  onDownloadAll: (jobId: string) => void; // Renamed batchId to jobId
}

export interface JobRowProps {
  job: JobItem; // Renamed batch to job
  downloadingFiles: Record<string, boolean>;
  onDownloadFileType: (jobId: string, fileType: string) => void; // Renamed batchId to jobId
  onDownloadAll: (jobId: string) => void; // Renamed batchId to jobId
}

export interface JobsHeaderProps {
  searchTerm: string;
  onSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFilterChange?: (filterName: string, value: any) => void;
  filters?: JobFilters;
}

export interface JobPaginationProps {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}
