/**
 * Type definitions for Ticket system components
 */

export type TicketStatus = 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TargetType = 'job' | 'task' | 'dataset';

export interface User {
  id: string;
  email: string;
}

export interface TicketTarget {
  id: number;
  ticket_id: number;
  target_type: TargetType;
  target_id: number;
}

export interface TicketMessage {
  id: number;
  ticket_id: number;
  author_id: string;
  body: string;
  created_at: string;
  author?: User;
}

export interface TicketStatusHistory {
  id: number;
  ticket_id: number;
  old_status: TicketStatus | null;
  new_status: TicketStatus;
  changed_by: string;
  changed_at: string;
}

export interface Ticket {
  id: string;
  creator_id: string;
  assignee_id?: string;
  title: string;
  description?: string;
  status: TicketStatus;
  priority: TicketPriority;
  created_at: string;
  updated_at: string;
  updated_by?: string;
  creator?: User;
  assignee?: User;
  ticket_targets?: TicketTarget[];
  ticket_messages?: TicketMessage[];
  ticket_status_history?: TicketStatusHistory[];
}

export interface CreateTicketRequest {
  title: string;
  description?: string;
  priority?: TicketPriority;
  target_type?: TargetType;
  target_id?: number;
}

export interface UpdateTicketRequest {
  title?: string;
  description?: string;
  status?: TicketStatus;
  priority?: TicketPriority;
  assignee_id?: string;
}

export interface CreateMessageRequest {
  body: string;
}

export interface TicketListProps {
  tickets: Ticket[];
  loading?: boolean;
  onTicketClick: (ticket: Ticket) => void;
  onStatusFilter?: (status: string) => void;
  onSearch?: (searchTerm: string) => void;
}

export interface TicketDetailProps {
  ticket: Ticket;
  onClose: () => void;
  onStatusChange?: (ticketId: string, status: TicketStatus) => void;
  onAddMessage?: (ticketId: string, message: string) => void;
}

export interface SupportDialogProps {
  open: boolean;
  onClose: () => void;
  supportType: 'task' | 'job' | 'general';
  taskId?: number;
  jobId?: string;
  onSubmit?: (ticket: Ticket) => void;
}
