/**
 * Variable-related type definitions
 * Centralized types for the variable system
 */

export interface VariableWithContext {
  name: string;
  source_level: 'Template' | 'Category' | 'Dataset';
  is_active: boolean;
  is_overridden: boolean;
  value: any;
  data?: any; // Alias for value
  gui_config?: any;
  gui?: any; // GUI configuration from database
  links?: any[];
  // Enhanced fields from variable hierarchy system
  level?: number; // Variable level (0=Global, 1+=Category, 999=Dataset)
  meta?: {
    required: boolean;
    constraints: {
      minLevel: number;
      maxLevel: number;
    };
    title: string;
    type: string;
  };
  editable_here?: boolean; // Whether variable can be edited at current level
  unset_required?: boolean; // Whether required variable is unset
}

export interface VariableTreeNode {
  id: number;
  name: string;
  description?: string;
  type: 'category' | 'dataset';
  level?: number;
  variables: VariableWithContext[];
  datasets?: VariableTreeNode[];
  children?: VariableTreeNode[];
}

export interface VariableTreeResponse {
  template_id: number;
  template_variables: VariableWithContext[];
  tree: VariableTreeNode[];
}

/**
 * Creates a variable change record for tracking modifications
 */
export interface VariableChange {
  variableName: string;
  oldValue: any;
  newValue: any;
  saveContext: {
    saveLevel: 'template' | 'category' | 'dataset';
    targetId?: number;
    targetType?: 'category' | 'dataset';
  };
  originalVariable: VariableWithContext;
  // Action type: 'set' (create/update override) or 'reset' (remove override)
  action?: 'set' | 'reset';
}

/**
 * Save context for determining where a variable should be saved
 */
export interface VariableSaveContext {
  saveLevel: 'template' | 'category' | 'dataset';
  targetId?: number;
  targetType?: 'category' | 'dataset';
}
