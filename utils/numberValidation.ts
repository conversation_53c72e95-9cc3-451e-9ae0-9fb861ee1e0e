// utils/numberValidation.ts

/**
 * Zählt die effektiven Nachkommastellen eines Werts.
 * Wird für Zahlen in wissenschaftlicher Notation (z. B. 1.234e-5)
 * verwendet, indem der Wert mit toFixed in eine Dezimalschreibweise konvertiert wird.
 */
export function countDecimals(num: number): number {
    const numStr = num.toString();
    if (numStr.includes('e')) {
      // Verwende toFixed mit hoher Präzision und entferne überflüssige Nullen am Ende
      const fixedStr = num.toFixed(20);
      const parts = fixedStr.split('.');
      if (parts.length < 2) return 0;
      const decimals = parts[1].replace(/0+$/, '');
      return decimals.length;
    } else {
      const parts = numStr.split('.');
      return parts[1] ? parts[1].length : 0;
    }
  }
  
  /**
   * <PERSON><PERSON><PERSON><PERSON> einen Rohwert in Kombination mit einem Multiplikator.
   * Es werden:
   * - Ganzzahligkeit (falls erforderlich),
   * - Mindest- und Maximalwerte sowie
   * - die maximale Anzahl an Dezimalstellen geprüft.
   *
   * Der zu validierende Wert wird vor der Prüfung mit gui.multiply multipliziert.
   */
  export function validateDisplayNumber(
    rawValue: number,
    gui: {
      integer?: boolean;
      min_value?: number;
      max_value?: number;
      max_digits?: number;
      multiply?: number;
    }
  ): string | null {
    const multiplyFactor = gui.multiply || 1;
    const displayNum = rawValue * multiplyFactor;
  
    if (gui.integer && !Number.isInteger(displayNum)) {
      return 'Must be an integer';
    }
    if (gui.min_value !== undefined && displayNum < gui.min_value) {
      return `Must be at least ${gui.min_value}`;
    }
    if (gui.max_value !== undefined && displayNum > gui.max_value) {
      return `Must be at most ${gui.max_value}`;
    }
    if (!gui.integer && gui.max_digits !== undefined) {
      const decimals = countDecimals(displayNum);
      if (decimals > gui.max_digits) {
        return `Maximum ${gui.max_digits} decimal places allowed`;
      }
    }
    return null;
  }
  