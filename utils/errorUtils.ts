/**
 * Utility functions for error handling
 */

/**
 * Extract error message from various error types
 */
export const getErrorMessage = (error: any, defaultMessage = 'An error occurred'): string => {
  if (!error) return defaultMessage;
  
  // Handle API response errors
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle standard Error objects
  if (error.message) {
    return error.message;
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  return defaultMessage;
};

/**
 * Safely execute an async function with error handling
 * @param asyncFn The async function to execute
 * @param errorHandler Function to handle errors
 * @returns A tuple with [data, error]
 */
export const safeAsync = async <T>(
  asyncFn: () => Promise<T>,
  errorHandler?: (error: any) => void
): Promise<[T | null, Error | null]> => {
  try {
    const result = await asyncFn();
    return [result, null];
  } catch (error) {
    if (errorHandler) {
      errorHandler(error);
    }
    return [null, error instanceof Error ? error : new Error(String(error))];
  }
};

/**
 * Create a download link for a blob
 */
export const downloadBlob = (blob: Blob, fileName: string): void => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
