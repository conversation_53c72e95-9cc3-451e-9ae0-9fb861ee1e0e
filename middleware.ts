import { type NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

export async function middleware(request: NextRequest) {
  // Check for API key authentication on API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const apiKey = request.headers.get('x-api-key');
    const validApiKey = process.env.API_KEY;

    if (validApiKey && apiKey === validApiKey) {
      // Allow API key authenticated requests to pass through
      return NextResponse.next({
        request: {
          headers: request.headers,
        },
      });
    }
  }

  const supabase = createClient();
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const { data: { user } } = await supabase.auth.getUser();

  if (!user && !request.nextUrl.pathname.startsWith('/login')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return response;
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};