# KI-Briefing: Hierarchische Variablen-Baumansicht Implementation

## Kontext für neuen KI-Chat

Du sollst eine Frontend-Komponente für eine hierarchische Variablen-Baumansicht entwickeln. Hier sind alle wichtigen Informationen:

## Projekt-Setup

- **Framework:** Next.js 14 mit TypeScript
- **UI Library:** Material-UI (MUI)
- **State Management:** Zustand stores (siehe `lib/stores/templateStore.ts`)
- **API Client:** Custom API service (`lib/services/api.ts`)
- **Database:** Supabase PostgreSQL

## Verfügbare APIs (bereits implementiert)

### 1. Tree API
```typescript
// GET /api/tree?withCounts=true
interface TreeResponse {
  success: boolean;
  data: TreeNode[];
}

interface TreeNode {
  id: number;
  parentId: number | null;
  name: string;
  path: string;  // ltree format: "1.2.3"
  level: number;
  datasetCnt?: number;
}

// Usage
const tree = await api.getTreeSkeleton(true);
```

### 2. Var-Nodes API
```typescript
// GET /api/var-nodes?key={variableName}&datasetId={id}&templateId={id}
interface VarNodeResponse {
  success: boolean;
  data: VarNode[];
}

interface VarNode {
  id: number;
  path: string;
  kind: 'cat' | 'ds' | 'tpl'; // category, dataset, template
  is_active: boolean; // true für höchste Priorität
}

// Usage
const varNodes = await api.getVarNodes('LEVERARM_X');
```

### 3. Variable Merge (RPC)
```sql
-- Supabase RPC function
SELECT * FROM merge_job_vars(template_id, ARRAY[dataset_ids]);
```

## Benötigte neue API

### Variable Tree API (`/api/variable-tree`) - **MUSS IMPLEMENTIERT WERDEN**
- **Zweck**: Vollständige Baumstruktur mit allen Variablen und deren Zuständen
- **Authentifizierung**: Erforderlich (`withAuth`)
- **Parameter**: `templateId` (erforderlich), `userId` (aus Auth)
- **RPC Funktion**: `get_variable_tree_with_context` - **MUSS ERSTELLT WERDEN**
- **Rückgabe**: Baumstruktur mit eingebetteten Variablen-Informationen

#### Erforderliche Datenstruktur
```typescript
interface VariableTreeNode {
  id: string;
  name: string;
  type: 'category' | 'dataset';
  variables: VariableWithContext[];
  children: VariableTreeNode[];
}

interface VariableWithContext {
  name: string;
  value: any;
  source_level: 'template' | 'category' | 'dataset';
  is_active: boolean;      // True wenn dieser Wert verwendet wird
  is_overridden: boolean;  // True wenn von höherer Priorität überschrieben
  defined_at_level: string; // Welche Ebene diese Variable ursprünglich definiert hat
}
```

#### Warum diese API benötigt wird
1. **Performance**: Aktuelle VarNodes API erfordert N+1 Abfragen (eine pro Dataset)
2. **Effizienz**: Ein einziger Aufruf gibt vollständigen Baum mit allen Variablen-Zuständen zurück
3. **Kontext**: Bietet Vererbungsinformationen für korrekte Farbkodierung
4. **Gemischte Zustände**: Ermöglicht Berechnung gemischter Zustände über Datasets in Kategorien hinweg

## Bestehende Komponenten (wiederverwenden)

### Variable Editor
```typescript
// components/template/InputRenderer.tsx
import InputRenderer from '@/components/template/InputRenderer';

<InputRenderer 
  variable={templateVariable} 
  template_data={templateData}
/>
```

### Template Store
```typescript
// lib/stores/templateStore.ts
const { templateVars, updateVariableData } = useTemplateStore();
```

### Variable Types
```typescript
// components/template/types/template.ts
interface TemplateVariable {
  name: string;
  links: any;
  data: any;
  component_name?: string;
  gui?: {
    component_id: string;
    group: string;
    order: number;
    label?: string;
    tooltip?: string;
    // ... weitere GUI-Eigenschaften
    required?: boolean;
    constraints?: { minLevel?: number; maxLevel?: number };
  };
}
```

#### Constraints & Required (GUI)
- Ebenen: Global = 0, Kategorien = 1..N (Tiefe), Dataset = 999.
- `gui.constraints.minLevel/maxLevel` steuern Editierbarkeit je Ebene; Inputs sind außerhalb deaktiviert.
- `gui.required` markiert erforderliche Variablen; UI zeigt roten Zustand, wenn entlang der Vererbung kein Wert gesetzt ist.

## Anforderungen

### UI Layout
```
┌─────────────────────────────────────────────────────────────┐
│ Template Dropdown: [GNSS Post Processing ▼]                │
├─────────────────────┬───────────────────────────────────────┤
│ Tree View (Links)   │ Variable Editor (Rechts)              │
│                     │                                       │
│ 📁 Kampagne 1       │ Variables for: Dataset XYZ            │
│ ├─ 🚗 Fahrzeug A    │ ┌─────────────────────────────────────┐ │
│ │  ├─ 📊 Dataset 1  │ │ LEVERARM_X: [1.234] (from Vehicle) │ │
│ │  └─ 📊 Dataset 2  │ │ LEVERARM_Y: [0.567] (from Dataset) │ │
│ └─ 🚗 Fahrzeug B    │ │ BASE_STATION: [AUTO] (from Template)│ │
│    └─ 📊 Dataset 3  │ └─────────────────────────────────────┘ │
└─────────────────────┴───────────────────────────────────────┘
```

### Funktionalität

1. **Template-Auswahl:** Dropdown lädt Baum neu
2. **Baum-Navigation:** 
   - Kategorie-Klick: Zeigt aggregierte Variablen
   - Dataset-Klick: Zeigt spezifische Variablen
3. **Variable-Highlighting:**
   - 🟢 Grün: Variable ist hier aktiv
   - 🟡 Gelb: Variable gesetzt, aber überschrieben
   - ⚪ Grau: Nur Template-Default
   - 🔴 Rot: Variable hat Validierungsfehler oder Konflikte

### Variable Highlighting System

#### Basic States
- **Green**: Variable is active (set and being used at this level)
- **Yellow**: Variable is set but overridden by a higher priority level
- **Gray**: Variable is defined at a higher level (Template/Category) but not overridden here
- **Red**: Variable has validation errors or conflicts

#### Mixed States (for Categories with Multiple Datasets)
When a variable has different states across datasets within a category:

**Primary Indicator (Main Color)**:
- Green: Variable is active in majority of datasets (>50%)
- Yellow: Variable is overridden in majority of datasets (>50%)
- Gray: Variable is not set in majority of datasets (>50%)

**Secondary Indicator (for Mixed States)**:
- Small colored dot or dashed border indicating minority state
- Example: Green badge with yellow dot = "Mostly active, but overridden in some datasets"

**Detailed Information**:
- Tooltip on hover: "Variable active in 3/5 datasets, overridden in 2/5"
- Click for detailed breakdown showing which specific datasets have which state

#### Visual Implementation Examples
```
🟢 Pure active state
🟢• Mixed: mostly active, some overridden (green with yellow dot)
🟡 Pure overridden state  
🟡• Mixed: mostly overridden, some active (yellow with green dot)
⚪ Pure not-set state
⚪• Mixed: mostly not-set, some active/overridden
```

#### UI Components
- **Badge System**: Material-UI Chip/Badge with primary and secondary colors
- **Tooltip**: Detailed breakdown with exact numbers
- **Legend**: Compact explanation of all possible states
- **Expandable Details**: Click to see per-dataset breakdown
4. **Variable-Editor:** Bestehende InputRenderer-Komponente nutzen

### Variable-Priorität (wichtig!)
1. Dataset (höchste Priorität)
2. Kategorie (tiefere überschreiben oberflächlichere)
3. Template (niedrigste Priorität)

## Implementierungs-Hinweise

### Empfohlene Komponenten-Struktur
```
VariableTreeView/
├── TemplateSelector.tsx
├── TreeView.tsx
├── VariableEditor.tsx
├── VariableStatusBadge.tsx
├── VariableTooltip.tsx
└── hooks/
    ├── useTreeData.ts
    ├── useVariableState.ts
    ├── useVariableHighlighting.ts
    └── useMixedStateCalculation.ts
```

```typescript
// Haupt-Container
const VariableTreeView: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<number>();
  const [selectedNode, setSelectedNode] = useState<{id: number, type: 'category' | 'dataset'}>();
  const [highlightedVariable, setHighlightedVariable] = useState<string>();
  
  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <TemplateSelector />
      <TreePanel />
      <VariablePanel />
    </Box>
  );
};

// Tree-Komponente
const TreePanel: React.FC = () => {
  // MUI TreeView oder custom implementation
};

// Variable-Panel
const VariablePanel: React.FC = () => {
  // Wiederverwendung von InputRenderer
};
```

### State Management
```typescript
// Erweitere bestehenden templateStore oder erstelle neuen
interface VariableTreeState {
  selectedTemplate: number | null;
  treeData: TreeNode[];
  selectedNode: {id: number, type: 'category' | 'dataset'} | null;
  variables: ContextualVariable[];
  highlightedVariable: string | null;
  mixedStates: Map<string, VariableState>; // Cache für gemischte Variable-States pro Kategorie
}

### Mixed State Calculation Logic
```typescript
interface VariableState {
  active: number;    // Count of datasets where variable is active
  overridden: number; // Count of datasets where variable is overridden
  notSet: number;    // Count of datasets where variable is not set
  total: number;     // Total number of datasets
}

function calculateMixedState(variableStates: VariableState): {
  primaryState: 'active' | 'overridden' | 'notSet';
  secondaryState?: 'active' | 'overridden' | 'notSet';
  isMixed: boolean;
} {
  const { active, overridden, notSet, total } = variableStates;
  const majority = Math.ceil(total / 2);
  
  // Determine primary state (majority)
  let primaryState: 'active' | 'overridden' | 'notSet';
  if (active >= majority) primaryState = 'active';
  else if (overridden >= majority) primaryState = 'overridden';
  else primaryState = 'notSet';
  
  // Check if mixed state exists
  const nonZeroStates = [active, overridden, notSet].filter(count => count > 0);
  const isMixed = nonZeroStates.length > 1;
  
  // Determine secondary state (largest minority)
  let secondaryState: 'active' | 'overridden' | 'notSet' | undefined;
  if (isMixed) {
    const states = [
      { type: 'active' as const, count: active },
      { type: 'overridden' as const, count: overridden },
      { type: 'notSet' as const, count: notSet }
    ];
    
    const sortedStates = states
      .filter(s => s.type !== primaryState && s.count > 0)
      .sort((a, b) => b.count - a.count);
    
    secondaryState = sortedStates[0]?.type;
  }
  
  return { primaryState, secondaryState, isMixed };
}
```

interface ContextualVariable extends TemplateVariable {
  source: {
    type: 'template' | 'category' | 'dataset';
    id: number;
    name: string;
  };
  isActive: boolean;
  isOverridden: boolean;
}
```

### API Integration
```typescript
// Erweitere lib/services/api.ts falls nötig
class ApiService {
  // Bereits vorhanden:
  async getTreeSkeleton(withCounts: boolean = true) { ... }
  async getVarNodes(key: string) { ... }
  
  // Möglicherweise hinzufügen:
  async getTemplates() { ... }
  async getVariablesForNode(nodeId: number, nodeType: 'category' | 'dataset') { ... }
}
```

## Wichtige Dateien zum Anschauen

1. **API Routes:**
   - `/app/api/tree/route.ts` - Tree API implementation
   - `/app/api/var-nodes/route.ts` - Var-nodes API implementation

2. **Bestehende Komponenten:**
   - `/components/template/InputRenderer.tsx` - Variable editor
   - `/components/template/types/template.ts` - Type definitions
   - `/lib/stores/templateStore.ts` - State management

3. **Services:**
   - `/lib/services/api.ts` - API client

## Entwicklungsreihenfolge

1. **Phase 1:** Basis-Layout mit Template-Dropdown und Tree-View
2. **Phase 2:** Variable-Anzeige im rechten Panel
3. **Phase 3:** Variable-Highlighting im Baum
4. **Phase 4:** Variable-Editing und Speichern
5. **Phase 5:** Polish und UX-Verbesserungen

## Testing

- **API Key:** `test-api-key-12345` (bereits konfiguriert)
- **Dev Server:** `npm run dev` (läuft auf Port 3001)
- **Test Endpoint:** `curl -H "x-api-key: test-api-key-12345" http://localhost:3001/api/tree`

### VariableStatusBadge Component Example
```typescript
interface VariableStatusBadgeProps {
  variableState: VariableState;
  variableName: string;
  onClick?: () => void;
}

const VariableStatusBadge: React.FC<VariableStatusBadgeProps> = ({
  variableState,
  variableName,
  onClick
}) => {
  const { primaryState, secondaryState, isMixed } = calculateMixedState(variableState);
  
  const getPrimaryColor = (state: string) => {
    switch (state) {
      case 'active': return 'success';
      case 'overridden': return 'warning';
      case 'notSet': return 'default';
      default: return 'default';
    }
  };
  
  const getTooltipText = () => {
    const { active, overridden, notSet, total } = variableState;
    return `Variable "${variableName}":\n` +
           `Aktiv: ${active}/${total} Datasets\n` +
           `Überschrieben: ${overridden}/${total} Datasets\n` +
           `Nicht gesetzt: ${notSet}/${total} Datasets`;
  };
  
  return (
    <Tooltip title={getTooltipText()} arrow>
      <Chip
        label={variableName}
        color={getPrimaryColor(primaryState)}
        size="small"
        onClick={onClick}
        sx={{
          position: 'relative',
          '&::after': isMixed ? {
            content: '""',
            position: 'absolute',
            top: 2,
            right: 2,
            width: 6,
            height: 6,
            borderRadius: '50%',
            backgroundColor: secondaryState === 'active' ? 'green' : 
                           secondaryState === 'overridden' ? 'orange' : 'gray'
          } : {}
        }}
      />
    </Tooltip>
  );
};
```

## Besondere Herausforderungen

1. **Performance:** Große Bäume mit vielen Datasets
2. **State Synchronisation:** Tree-Zustand mit Variable-Zustand
3. **Visual Feedback:** Klare Anzeige der Variable-Vererbung
4. **Mixed State Calculation:** Performance-optimierte Berechnung für große Datasets
5. **UX:** Intuitive Navigation zwischen Baum und Variablen für komplexe Zustände

## Erfolgs-Kriterien

- [ ] Template-Auswahl funktioniert
- [ ] Baum zeigt Kategorien und Datasets hierarchisch
- [ ] Variable-Highlighting zeigt Vererbung korrekt
- [ ] Variable-Editor ermöglicht Änderungen
- [ ] Änderungen werden korrekt gespeichert
- [ ] UI ist responsive und benutzerfreundlich

## Implementation Steps

### Phase 1: Database & API Layer
1. **Create RPC Function `get_variable_tree_with_context`**
   ```sql
   CREATE OR REPLACE FUNCTION get_variable_tree_with_context(
     p_template_id UUID,
     p_user_id UUID
   )
   RETURNS JSON
   LANGUAGE plpgsql
   SECURITY DEFINER
   AS $$
   DECLARE
     result JSON;
   BEGIN
     -- Complex query joining categories, datasets, template_variables, dataset_variables
     -- with CASE statements to determine source_level, is_active, is_overridden
     -- Returns hierarchical JSON structure
   END;
   $$;
   ```

2. **Create API Route `/api/variable-tree`**
   ```typescript
   // app/api/variable-tree/route.ts
   export async function GET(request: NextRequest) {
     const { searchParams } = new URL(request.url);
     const templateId = searchParams.get('templateId');
     
     const { data } = await supabase.rpc('get_variable_tree_with_context', {
       p_template_id: templateId,
       p_user_id: userId
     });
     
     return NextResponse.json(data);
   }
   ```

### Phase 2: Frontend Components
3. **Create VariableTreeView component structure**
4. **Implement API integration hooks**
5. **Build variable highlighting logic with new color scheme**
6. **Integrate with existing InputRenderer**

### Phase 3: Advanced Features
7. **Add state management for tree navigation**
8. **Implement variable editing workflow**
9. **Add performance optimizations**
10. **Test with real data**

## Critical Implementation Notes

### RPC Function Requirements
- Must handle user permissions (RLS)
- Should include template variable inheritance
- Must calculate is_active and is_overridden correctly
- Should be optimized for large trees (consider pagination)
- Must return consistent JSON structure

### API Performance Considerations
- Cache results on frontend
- Consider WebSocket updates for real-time changes
- Implement request debouncing
- Add loading states for large trees

Viel Erfolg bei der Implementierung! 🚀
