'use client'

import React from 'react';
import { Drawer, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DataObjectIcon from '@mui/icons-material/DataObject';
import StorageIcon from '@mui/icons-material/Storage';
import DescriptionIcon from '@mui/icons-material/Description';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import { Box, Button, Divider } from '@mui/material';
import Link from 'next/link';

const Sidebar = () => {
  const drawerWidth = 190;

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: {
          width: drawerWidth,
          boxSizing: 'border-box',
          mt: 8,
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100% - 64px)', // Subtract the top margin
        },
      }}
    >
      <List>
        {[
          { text: 'Dashboard', icon: <DashboardIcon />, href: '/' },
          { text: 'Files', icon: <AttachFileIcon />, href: '/files' },
          { text: 'Datasets', icon: <StorageIcon />, href: '/datasets' },
          { text: 'Templates', icon: <DescriptionIcon />, href: '/templates' },
          { text: 'Jobs', icon: <DataObjectIcon />, href: '/jobs' },
        ].map((item) => (
          <Link href={item.href} key={item.text} passHref style={{ textDecoration: 'none', color: 'inherit' }}>
            <ListItem>
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItem>
          </Link>
        ))}
      </List>
      
      {/* Trennung zwischen Navigation und Action-Button */}
      <Divider sx={{ mt: 2, mb: 2 }} />
      
      <Box sx={{
        px: 2,
        pb: 2
      }}>
        <Link href="/jobs/create" passHref style={{ textDecoration: 'none' }}>
          <Button
            variant="contained"
            startIcon={<PlayArrowIcon />}
            fullWidth
            sx={{
              backgroundColor: 'secondary.main',
              '&:hover': {
                backgroundColor: 'secondary.dark',
              },
              py: 1.5,
              fontWeight: 600
            }}
          >
            New Job
          </Button>
        </Link>
      </Box>
      
      {/* Freier Raum unter dem Button */}
      <Box sx={{ flex: 1 }} />
    </Drawer>
  );
};

export default Sidebar;