// filepath: /Users/<USER>/tokeep/mvp/algonav-cloud-gui/components/common/SupportIcon.jsx
import React from 'react';
import { useTheme } from '@mui/material/styles';

const SupportIcon = ({ width = 24, height = 24, className = '', color }) => {
  const theme = useTheme();

  // Use provided color or default to current color (black)
  const fillColor = color || theme.palette.text.primary;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 735.63324 701.22662"
      className={className}
      style={{ display: 'block' }}
    >
      <path
        d="m 140.27024,555.19104 c -80.257999,-7.166 -136.7701988,-70.632 -139.85859879,-150.544 -0.7092,-18.348 -0.2866,-36.828 -0.2876,-55.194 l -0.0022,-160.624 c -0.0054,-22.6 -1.0596,-45.526 3.88479999,-67.736 C 19.538041,51.327035 79.804241,2.8440353 151.10424,0.24363528 c 4.424,-0.5074 9.184,-0.0684 13.642,-0.046 l 413.96,-0.0724 c 93.4,-0.1954 156.768,71.79979972 156.89,163.36780472 l 0.002,213.548 c 0.004,21.836 0.734,43.462 -4.678,64.826 -14.764,58.294 -65.946,105.528 -125.96,113.046 -23.124,2.896 -47.226,1.538 -70.502,1.54 l -94.63,-0.004 h -119.222 c -10.048,8.454 -18.856,18.982 -28.208,28.2 -33.12,32.646 -66.128,65.604 -98.636,98.864 -4.696,4.806 -9.274,10.054 -14.8,13.924 -12.942,9.06 -34.506,0.964 -37.696,-14.632 -2.22,-10.85 -1.008,-31.59 -1.01,-43.184 z m 54.68,-505.066005 c -42.624,-0.062 -79.286,-3.7928 -112.535999,29.574 -29.8476,29.952005 -30.4562,56.318005 -30.3804,95.162005 l 0.0298,222.086 c 0.347,55.154 29.1706,98.722 85.530599,107.118 17.566,2.618 42.164,-2.926 50.144,18.908 5.014,13.72 2.208,76.112 2.194,93.132 7.564,-8.104 15.828,-15.616 23.682,-23.45 l 66.34,-66.424 c 27.402,-28.056 35.668,-20.368 73.322,-20.36 l 81.552,0.034 136.85,0.026 c 52.346,0.228 97.964,-18.864 110.808,-76.342 3.64,-16.292 2.776,-34.024 2.766,-50.644 l 0.1,-226.202 c -0.302,-48.246 -36.492,-92.732005 -84.214,-101.398005 -14.436,-2.62 -30.144,-1.212 -44.808,-1.214 z"
        fill={fillColor}
        strokeWidth="2"
      />
      <path
        d="m 383.19224,277.93104 c 22.394,-8.244 37.444,-29.69 36.542,-53.882 -2.226,-59.684 -85.774,-70.656 -103.068,-12.688 -2.91,9.758 -0.898,19.68 -8.542,27.85 -15.92,17.012 -41.006,4.77 -41.78,-17.102 -1.872,-52.774 45.618,-97.904 97.716,-99.286 43.012,-5.194 87.828,31.734 99.91,71.304 17.292,56.632 -13.248,116.906 -70.392,134.106 0.85,42.434 -42.144,34.716 -48.888,13.05 -2.404,-7.728 -2.002,-32.376 -0.66,-40.92 3.53,-22.5 22.712,-17.608 39.162,-22.432 z"
        fill={fillColor}
        strokeWidth="2"
      />
      <path
        d="m 372.89224,422.43304 c -34.304,4.668 -40.522,-44.996 -8.832,-51.466 33.598,-1.906 41.648,43.03 8.832,51.466 z"
        fill={fillColor}
        strokeWidth="2"
      />
    </svg>
  );
};

export default SupportIcon;