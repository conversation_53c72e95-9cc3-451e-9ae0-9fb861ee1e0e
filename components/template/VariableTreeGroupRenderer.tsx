import React, { forwardRef, useImperative<PERSON>andle, useMemo, useState } from 'react'
import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  Chip,
  Fade
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import SettingsIcon from '@mui/icons-material/Settings'
import GpsFixedIcon from '@mui/icons-material/GpsFixed'
import SpeedIcon from '@mui/icons-material/Speed'
import RotateRightIcon from '@mui/icons-material/RotateRight'
import OutputIcon from '@mui/icons-material/Output'
import FolderIcon from '@mui/icons-material/Folder'
import { VariableWithContext } from '@/types/variable'

interface VariableTreeGroupRendererProps {
  groupName: string
  variables: VariableWithContext[]
  // Render prop to render a single variable row
  renderVariable: (variable: VariableWithContext) => React.ReactNode
  defaultExpanded?: boolean
}

export interface VariableTreeGroupRendererRef {
  expandGroup: () => void
  collapseGroup: () => void
  isExpanded: () => boolean
}

// Helper function to get appropriate icon for each group (aligned with ListGroupRenderer)
function getGroupIcon(groupName: string) {
  const name = groupName.toLowerCase()
  if (name.includes('gnss') || name.includes('position')) return GpsFixedIcon
  if (name.includes('imu') || name.includes('motion')) return RotateRightIcon
  if (name.includes('velocity') || name.includes('speed')) return SpeedIcon
  if (name.includes('output')) return OutputIcon
  if (name.includes('constraint')) return SettingsIcon
  return FolderIcon // Default icon
}

// Helper function to get group color theme (aligned with ListGroupRenderer)
function getGroupTheme(groupName: string) {
  const name = groupName.toLowerCase()
  if (name.includes('gnss') || name.includes('position')) return '#4CAF50' // Green
  if (name.includes('imu') || name.includes('motion')) return '#2196F3' // Blue
  if (name.includes('velocity') || name.includes('speed')) return '#FF9800' // Orange
  if (name.includes('output')) return '#9C27B0' // Purple
  if (name.includes('constraint')) return '#F44336' // Red
  return '#FC6200' // Default AlgoNav orange
}

// Group variables by dependency chains (base variable and its dependents), sorted by gui.order
function groupVariablesWithDependencies(variables: VariableWithContext[]): VariableWithContext[][] {
  const groups: VariableWithContext[][] = []
  const processed = new Set<string>()

  const sorted = [...variables].sort((a, b) =>
    ((a.gui?.order ?? Number.POSITIVE_INFINITY) - (b.gui?.order ?? Number.POSITIVE_INFINITY))
  )

  const dependentsOf = (base: VariableWithContext) =>
    sorted.filter(v => v.gui?.dependson_var === base.name)

  const process = (v: VariableWithContext) => {
    if (processed.has(v.name)) return
    const group: VariableWithContext[] = [v]
    processed.add(v.name)
    const deps = dependentsOf(v)
    deps.forEach(d => {
      if (!processed.has(d.name)) {
        group.push(d)
        processed.add(d.name)
      }
    })
    groups.push(group)
  }

  sorted.forEach(v => {
    if (!v.gui?.dependson_var) process(v)
  })

  return groups
}

const VariableTreeGroupRenderer = forwardRef<VariableTreeGroupRendererRef, VariableTreeGroupRendererProps>(
  ({ groupName, variables, renderVariable, defaultExpanded = false }, ref) => {
    const [isExpanded, setIsExpanded] = useState<boolean>(defaultExpanded)

    const variableGroups = useMemo(() => groupVariablesWithDependencies(variables), [variables])
    const GroupIcon = getGroupIcon(groupName)
    const groupTheme = getGroupTheme(groupName)
    const fieldCount = variables.length

    useImperativeHandle(ref, () => ({
      expandGroup: () => setIsExpanded(true),
      collapseGroup: () => setIsExpanded(false),
      isExpanded: () => isExpanded,
    }), [isExpanded])

    return (
      <Box id={`group-${groupName.toLowerCase().replace(/\s+/g, '-')}`} sx={{ mb: 4 }}>
        <Accordion
          expanded={isExpanded}
          onChange={(_, expanded) => setIsExpanded(expanded)}
          className="template-group-accordion"
          sx={{
            bgcolor: 'transparent',
            borderRadius: 2,
            boxShadow: isExpanded
              ? '0 8px 24px rgba(48, 44, 89, 0.2)'
              : '0 4px 12px rgba(48, 44, 89, 0.12)',
            border: '1px solid',
            borderColor: 'divider',
            transition: 'box-shadow 0.2s ease',
            '&:before': { display: 'none' },
            '&:hover': { boxShadow: '0 8px 32px rgba(48, 44, 89, 0.18)' },
            borderLeft: `4px solid ${groupTheme}`,
            '& .MuiAccordionSummary-root': {
              bgcolor: 'transparent',
              minHeight: 80,
              px: 3,
              py: 2,
              transition: 'background-color 0.15s ease',
              '&:hover': { bgcolor: 'action.hover' },
              '&.Mui-expanded': { minHeight: 80, borderBottom: '1px solid rgba(255, 255, 255, 0.1)' },
            },
            '& .MuiAccordionSummary-content': { margin: 0, alignItems: 'center', '&.Mui-expanded': { margin: 0 } },
            '& .MuiAccordionSummary-expandIconWrapper': {
              color: 'text.secondary',
              transition: 'transform 0.2s ease, color 0.15s ease',
              '&:hover': { color: 'text.primary' },
              '&.Mui-expanded': { transform: 'rotate(180deg)' },
            },
            '& .MuiAccordionDetails-root': { bgcolor: 'transparent', p: 0 },
          }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}> 
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', pl: 1 }}>
              <Avatar sx={{ bgcolor: groupTheme, mr: 3, width: 48, height: 48, boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)' }}>
                <GroupIcon sx={{ fontSize: 24 }} />
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600, fontSize: '1.125rem', lineHeight: 1.2, mb: 0.5 }}>
                  {groupName}
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.75rem', fontWeight: 400 }}>
                  {fieldCount} field{fieldCount !== 1 ? 's' : ''} • {isExpanded ? 'Expanded' : 'Collapsed'}
                </Typography>
              </Box>
              <Fade in={!isExpanded}>
                <Chip
                  label="Configure"
                  size="small"
                  sx={{ mr: 2, bgcolor: 'action.hover', color: 'text.primary', fontWeight: 500, '&:hover': { bgcolor: 'action.selected' } }}
                />
              </Fade>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ p: 4, pt: 3 }}>
              {variableGroups.map((group, groupIndex) => (
                <Paper
                  key={groupIndex}
                  elevation={0}
                  sx={{
                    p: 4,
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 3,
                    bgcolor: 'background.paper',
                    mb: groupIndex < variableGroups.length - 1 ? 3 : 0,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                    transition: 'box-shadow 0.15s ease',
                    '&:hover': { boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)' },
                    borderTop: `2px solid ${groupTheme}`,
                  }}
                >
                  {group.map((variable) => (
                    <Box key={variable.name} sx={{ '&:not(:last-child)': { mb: 3, pb: 3, borderBottom: '1px solid', borderColor: 'divider' } }}>
                      {renderVariable(variable)}
                    </Box>
                  ))}
                </Paper>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    )
  }
)

VariableTreeGroupRenderer.displayName = 'VariableTreeGroupRenderer'

export default VariableTreeGroupRenderer
