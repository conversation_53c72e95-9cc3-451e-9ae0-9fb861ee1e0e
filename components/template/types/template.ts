export interface TemplateVariable {
  name: string;
  links: any;  // New field
  data: any;
  component_name?: string; // Added for component mapping
  gui?: {
    component_id: string;
    group: string;
    order: number;
    label?: string;
    tooltip?: string;
    min_value?: number;
    max_value?: number;
    max_digits?: number;
    max_chars?: number;
    integer?: boolean;
    items?: Array<{
      label: string;
      value: any;
    }>;
    value_checked?: any;
    value_unchecked?: any;
    min_checked?: number;
    max_checked?: number;
    msg_below_min?: string;
    dependson_var?: string;
    dependson_val?: any[];
    // DynamicState specific properties
    axes?: string[];               // Labels for each axis (e.g., ["X", "Y", "Z"])
    visible_axes?: number[];       // Optional subset of axes to display
    unit?: string;                // Unit for the values
    process_type?: string[];      // Available process types for selection
  };
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  vars: TemplateVariable[];
  template_data?: any;
  dynamic_state?: {
    axes?: string[];
    visible_axes?: number[];
    unit?: string;
    process_type?: string[];
  };
}

export interface GUIComponent {
  id: string;
  description: string;
  component_name: string;
  parameters: {
    type: string;
    defaultValue?: any;
    [key: string]: any;
  }
}
