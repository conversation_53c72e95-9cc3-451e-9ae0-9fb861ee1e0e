import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Button,
  Snackbar
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import {
  Save as SaveIcon
} from '@mui/icons-material';
import { useVariableTree } from '../../lib/hooks/useVariableTree';
import { useVariableTreeState } from '../../lib/hooks/useVariableTreeState';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableInputRenderer } from './VariableInputRenderer';
import { VariableFocusIndicator } from './VariableFocusIndicator';
import { CustomTreeItem } from './CustomTreeItem';
import VariableTreeGroupRenderer from './VariableTreeGroupRenderer';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
  // Optional control (e.g., template selector) to render in the left panel header
  templateControl?: React.ReactNode;
  // Selected template name for Global node label
  templateName?: string;
  // A key that changes when template selection changes (to trigger highlight)
  templateChangeKey?: string | number;
}

interface TreeItemData {
  id: string;
  label: string;
  type: 'category' | 'dataset' | 'global';
  variables?: any[];
  children?: TreeItemData[];
  nodeId?: number;
  datasetId?: number;
}

// Build tree structure from the new hierarchical API response with Global root node
const buildTreeFromVariableData = (variableTreeNodes: any[], templateVariables: any[], templateName?: string): TreeItemData[] => {
  const convertNode = (node: any): TreeItemData => {
    const treeItem: TreeItemData = {
      id: `category-${node.id}`,
      label: node.description ? `${node.name} (${node.description})` : node.name,
      type: 'category',
      variables: node.variables || [],
      children: [],
      nodeId: node.id
    };

    // Add datasets as children if they exist
    if (node.datasets && Array.isArray(node.datasets)) {
      node.datasets.forEach((dataset: any) => {
        const datasetItem: TreeItemData = {
          id: `dataset-${dataset.id}`,
          label: dataset.description ? `${dataset.name} (${dataset.description})` : dataset.name,
          type: 'dataset',
          variables: dataset.variables || [],
          nodeId: node.id,
          datasetId: dataset.id
        };
        treeItem.children!.push(datasetItem);
      });
    }

    // Add child categories recursively if they exist
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((childNode: any) => {
        const childItem = convertNode(childNode);
        treeItem.children!.push(childItem);
      });
    }

    return treeItem;
  };

  // Create Global root node
  const globalRoot: TreeItemData = {
    id: 'global-root',
    label: templateName ? `Global (${templateName})` : 'Global',
    type: 'global',
    variables: templateVariables || [],
    children: variableTreeNodes.map(convertNode)
  };

  return [globalRoot];
};

// Helper function to sort tree items recursively
const sortTreeItems = (items: TreeItemData[]): TreeItemData[] => {
  return items
    .sort((a, b) => a.label.localeCompare(b.label))
    .map(item => ({
      ...item,
      children: item.children ? sortTreeItems(item.children) : undefined
    }));
};

const findTreeNodeById = (items: TreeItemData[], id: string): TreeItemData | null => {
  for (const item of items) {
    if (item.id === id) {
      return item;
    }
    if (item.children) {
      const found = findTreeNodeById(item.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const getContextNodeType = (node: TreeItemData | null): 'category' | 'dataset' | undefined => {
  if (!node || node.type === 'global') {
    return undefined;
  }
  return node.type;
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect,
  templateControl,
  templateName,
  templateChangeKey
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeError, setTreeError] = useState<string | null>(null);
  const [showInputs, setShowInputs] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [showErrorSnackbar, setShowErrorSnackbar] = useState(false);
  const [flashGlobal, setFlashGlobal] = useState(false);
  const firstTemplateRenderRef = useRef(true);

  // Right-panel scroll management: per-level scroll positions
  const variableListContainerRef = useRef<HTMLDivElement | null>(null);
  const scrollPositionsRef = useRef<Record<string, number>>({});
  const currentLevelIdRef = useRef<string | null>(null);
  const isRestoringScrollRef = useRef<boolean>(false);

  const selectedNode = React.useMemo(() => {
    if (!selectedItems) {
      return null;
    }
    return findTreeNodeById(treeItems, selectedItems);
  }, [selectedItems, treeItems]);


  const getLevelKey = useCallback((node: TreeItemData | null, explicitId?: string | null) => {
    if (explicitId) return explicitId;
    if (!node) return 'none';
    return node.id; // ids are already like 'global-root' | 'category-<id>' | 'dataset-<id>'
  }, []);

  const {
    data,
    loading,
    error,
    refetch,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    focusVariable,
    unfocusVariable,
    isVariableFocused,
    focusedVariable,
    getInheritedValueForContext
  } = useVariableTree({ templateId });

  // State management for variable changes
  const {
    hasChanges,
    updateVariable,
    resetAllChanges,
    saveChanges,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged,
    isVariableResetPending
  } = useVariableTreeState({
    templateId,
    onSaveSuccess: () => {
      console.log('Variables saved successfully');
      setSaveSuccess(true);
      // Refetch the variable tree data to reflect changes
      refetch();
    },
    onSaveError: (error) => {
      console.error('Failed to save variables:', error);
      setShowErrorSnackbar(true);
    }
  });
  console.log('Variable tree data:', data);
  // Build tree from variable tree data when it's available

  useEffect(() => {
    if (data?.tree) {
      setTreeLoading(true);
      try {
        // Build tree directly from variable tree data with Global root
        const hierarchicalTree = buildTreeFromVariableData(
          data.tree,
          data.template_variables || [],
          templateName || 'Template'
        );
        console.log('Built hierarchical tree:', hierarchicalTree);
        // Set current level id to default Global root for correct initial scroll handling
        currentLevelIdRef.current = 'global-root';

        setTreeItems(sortTreeItems(hierarchicalTree));

        // Auto-expand Global root and first level categories
        const expandIds = ['global-root'];
        if (hierarchicalTree[0]?.children) {
          expandIds.push(...hierarchicalTree[0].children.map(item => item.id));
        }
        console.log('Auto-expanding items:', expandIds);
        setExpandedItems(expandIds);

        // Default select Global root
        setSelectedItems('global-root');
        // Initialize current level ref to global-root
        currentLevelIdRef.current = 'global-root';
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setTreeError(errorMessage);
        console.error('Error building tree:', err);
      } finally {
        setTreeLoading(false);
        console.log('Tree items:', treeItems);
      }
    }
  }, [data, templateName]);

  // Flash the global node when the template selection changes
  useEffect(() => {
    if (firstTemplateRenderRef.current) {
      firstTemplateRenderRef.current = false;
      return;
    }
    if (templateChangeKey !== undefined) {
      setFlashGlobal(true);
      const t = setTimeout(() => setFlashGlobal(false), 900);
      return () => clearTimeout(t);
    }
  }, [templateChangeKey]);

  // Helper function to get variables defined directly in a specific node (not inherited)
  const getVariablesDefinedInNode = React.useCallback((nodeId?: number, datasetId?: number): string[] => {
    if (!data) return [];

    // Handle Global root (template) level: include template variables
    if (nodeId === undefined && datasetId === undefined) {
      return (data.template_variables || []).map((v: any) => v.name);
    }

    const variableNames = new Set<string>();

    // Find the specific node
    const findNode = (nodes: any[], targetNodeId: number): any => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return node;
        }
        if (node.children) {
          const found = findNode(node.children, targetNodeId);
          if (found) return found;
        }
      }
      return null;
    };

    if (nodeId !== undefined) {
      const targetNode = findNode(data.tree, nodeId);
      if (targetNode) {
        // Add variables defined directly in this node
        if (targetNode.variables) {
          targetNode.variables.forEach((v: any) => variableNames.add(v.name));
        }

        // If we're looking at a specific dataset, add its variables too
        if (datasetId !== undefined && targetNode.datasets) {
          const targetDataset = targetNode.datasets.find((d: any) => d.id === datasetId);
          if (targetDataset && targetDataset.variables) {
            targetDataset.variables.forEach((v: any) => variableNames.add(v.name));
          }
        }
      }
    }

    return Array.from(variableNames);
  }, [data]);

  // Helper function to collect all unique variable names for a context (including inherited)
  const getAllVariablesForContext = React.useCallback((nodeId?: number, datasetId?: number) => {
    if (!data) return [];

    const allVariableNames = new Set<string>();

    // Add template variables
    data.template_variables.forEach((v: any) => allVariableNames.add(v.name));

    // Find the path to the current context
    const findNodePath = (nodes: any[], targetNodeId: number): any[] => {
      for (const node of nodes) {
        if (node.id === targetNodeId) {
          return [node];
        }
        if (node.children) {
          const childPath = findNodePath(node.children, targetNodeId);
          if (childPath.length > 0) {
            return [node, ...childPath];
          }
        }
      }
      return [];
    };

    if (nodeId) {
      const nodePath = findNodePath(data.tree, nodeId);

      // Add variables from all nodes in the path (inheritance chain)
      nodePath.forEach(node => {
        if (node.variables) {
          node.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      });

      // If we're looking at a specific dataset, add its variables too
      if (datasetId && nodePath.length > 0) {
        const parentNode = nodePath[nodePath.length - 1];
        const targetDataset = parentNode.datasets?.find((d: any) => d.id === datasetId);
        if (targetDataset && targetDataset.variables) {
          targetDataset.variables.forEach((v: any) => allVariableNames.add(v.name));
        }
      }
    }

    return Array.from(allVariableNames).sort();
  }, [data]);

  // Navigate to the defining level of a variable (context-aware)
  const navigateToDefiningLevel = useCallback((variableName: string) => {
    if (!data) return;

    // Determine current context from the selected node
    const contextNodeId = selectedNode?.type === 'global' ? undefined : selectedNode?.nodeId;
    const contextDatasetId = selectedNode?.type === 'dataset' ? selectedNode?.datasetId : undefined;

    // Use context-aware state to find the actually active/effective variable
    const state = getVariableStateForContext(variableName, contextNodeId, contextDatasetId);
    const active = state.activeVariable; // shape: { variable, level, path, nodeId?, datasetId? }
    if (!active) return;

    // Helper: find path of category ancestors to a target category id
    const findCategoryPathById = (nodes: any[], targetId: number, acc: any[] = []): any[] | null => {
      for (const node of nodes) {
        const currentPath = [...acc, node];
        if (node.id === targetId) return currentPath;
        if (node.children) {
          const found = findCategoryPathById(node.children, targetId, currentPath);
          if (found) return found;
        }
      }
      return null;
    };

    // Build target selection and expansion path
    let targetNodeId: string = 'global-root';
    let pathToExpand: string[] = ['global-root'];

    if (active.level === 0 || (!active.nodeId && !active.datasetId)) {
      // Template level
      targetNodeId = 'global-root';
      pathToExpand = ['global-root'];
    } else if (active.datasetId) {
      // Dataset level: include all ancestor categories, then dataset
      const catPath = active.nodeId ? findCategoryPathById(data.tree || [], active.nodeId) : null;
      if (catPath && catPath.length > 0) {
        pathToExpand = ['global-root', ...catPath.map((n: any) => `category-${n.id}`), `dataset-${active.datasetId}`];
      } else {
        // Fallback: expand just category and dataset if path not found
        pathToExpand = ['global-root', active.nodeId ? `category-${active.nodeId}` : 'global-root', `dataset-${active.datasetId}`];
      }
      targetNodeId = `dataset-${active.datasetId}`;
    } else if (active.nodeId) {
      // Category level: include all ancestor categories
      const catPath = findCategoryPathById(data.tree || [], active.nodeId);
      if (catPath && catPath.length > 0) {
        pathToExpand = ['global-root', ...catPath.map((n: any) => `category-${n.id}`)];
      } else {
        pathToExpand = ['global-root', `category-${active.nodeId}`];
      }
      targetNodeId = `category-${active.nodeId}`;
    }

    // Expand the path to the target node
    setExpandedItems(prev => {
      const newExpanded = new Set([...prev, ...pathToExpand]);
      return Array.from(newExpanded);
    });

    // Set restoring flag immediately to prevent scroll events during navigation
    isRestoringScrollRef.current = true;

    // Before switching, save current level scroll position
    const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
    if (variableListContainerRef.current && currentKey) {
      scrollPositionsRef.current[currentKey] = variableListContainerRef.current.scrollTop;
      // Debug: Saving scroll before navigation
      // console.log('💾 Saving scroll before navigation:', {
      //   fromKey: currentKey,
      //   scrollTop: variableListContainerRef.current.scrollTop
      // });
    }

    // Select the target node
    setSelectedItems(targetNodeId);
    // Update current level id for scroll tracking
    currentLevelIdRef.current = targetNodeId;

    // After selection state updates and right panel renders, restore its saved scroll first
    setTimeout(() => {
      const targetKey = targetNodeId;
      const savedScroll = scrollPositionsRef.current[targetKey] ?? 0;

      // Debug: Restoring scroll for navigation target
      // console.log('🎯 Restoring scroll for navigation target:', {
      //   targetKey,
      //   savedScroll,
      //   allScrollPositions: { ...scrollPositionsRef.current }
      // });

      if (variableListContainerRef.current) {
        // Ensure the flag is set during restoration
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = savedScroll;

        setTimeout(() => {
          isRestoringScrollRef.current = false;
          // console.log('✅ Navigation scroll restoration complete for:', targetKey);
        }, 200);
      }

      // Then scroll the specific variable into view within the right panel container
      setTimeout(() => {
        const container = variableListContainerRef.current;
        const el = document.querySelector(`[data-variable-name="${variableName}"]`) as HTMLElement | null;
        if (container && el) {
          // Compute position relative to container and center it
          const containerRect = container.getBoundingClientRect();
          const elRect = el.getBoundingClientRect();
          const delta = elRect.top - containerRect.top;
          const targetTop = container.scrollTop + delta - container.clientHeight / 2 + elRect.height / 2;
          container.scrollTo({ top: Math.max(0, targetTop), behavior: 'smooth' });

          // Add highlight effect
          const originalBackground = el.style.backgroundColor;
          const originalTransition = el.style.transition;
          el.style.backgroundColor = '#fff3cd';
          el.style.transition = 'background-color 0.3s ease';
          setTimeout(() => {
            el.style.backgroundColor = originalBackground;
            el.style.transition = originalTransition;
          }, 3000);
        }
      }, 150);
    }, 150);

  }, [data, selectedNode, getVariableStateForContext, getLevelKey]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (!selectedItems || !selectedNode) {
      return;
    }

    // Restore per-level scroll position after selection updates
    setTimeout(() => {
      const key = getLevelKey(selectedNode, selectedItems);
      const saved = scrollPositionsRef.current[key] ?? 0;

      // Debug: Restoring scroll position
      // console.log('📍 Restoring scroll position:', {
      //   levelKey: key,
      //   savedScrollTop: saved,
      //   allScrollPositions: { ...scrollPositionsRef.current }
      // });

      if (variableListContainerRef.current) {
        // Ensure the flag is still set during restoration
        isRestoringScrollRef.current = true;
        variableListContainerRef.current.scrollTop = saved;

        // Reset the flag after a longer delay to ensure all scroll events are processed
        setTimeout(() => {
          isRestoringScrollRef.current = false;
          // console.log('✅ Scroll restoration complete for level:', key);
        }, 200);
      }
    }, 0);
  }, [selectedItems, selectedNode, getLevelKey]);

  // Handle ESC key to unfocus variable
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && focusedVariable) {
        unfocusVariable();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusedVariable, unfocusVariable]);

  if (loading || treeLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || treeError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading data: {error || treeError}
        <Box sx={{ mt: 1 }}>
          <Chip
            label="Try again"
            onClick={refetch}
            size="small"
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data && treeItems.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No data available
      </Alert>
    );
  }

  // Handle click on empty area to unfocus variable
  const handleBackgroundClick = (event: React.MouseEvent) => {
    // Only unfocus if clicking directly on the Paper background
    if (event.target === event.currentTarget && focusedVariable) {
      unfocusVariable();
    }
  };

  return (
    <Paper
      sx={{ p: 2 }}
      onClick={handleBackgroundClick}
    >
      {/* Header with Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Typography variant="h6">
          Variable Tree (Template ID: {templateId})
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {/* Save/Reset Controls */}
          {hasChanges && (
            <>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={saveChanges}
                disabled={isSaving}
                color="primary"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={resetAllChanges}
                disabled={isSaving}
                color="secondary"
              >
                Reset
              </Button>
            </>
          )}

          <Button
            variant="outlined"
            size="small"
            onClick={() => setShowInputs(!showInputs)}
          >
            {showInputs ? 'STATUS BADGES' : 'INPUT COMPONENTS'}
          </Button>
        </Box>
      </Box>

      {/* Save Error */}
      {saveError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error saving: {saveError}
        </Alert>
      )}

      {/* Two-column layout */}
      <Grid container spacing={2}>
        {/* Left column: Tree */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper sx={{ p: 2, height: 'calc(100vh - 240px)', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              {/* Only dropdown on this line for space */}
              {templateControl}
            </Box>
            {treeItems.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => {
                  // Set restoring flag immediately to prevent scroll events during transition
                  isRestoringScrollRef.current = true;

                  // Save current level scroll position before switching
                  // Use currentLevelIdRef instead of selectedNode to get the current key
                  const currentKey = currentLevelIdRef.current || getLevelKey(selectedNode);
                  const currentScrollTop = variableListContainerRef.current?.scrollTop || 0;

                  // Debug: Level switching
                  // console.log('🔄 Level switching:', {
                  //   from: currentKey,
                  //   to: itemId,
                  //   savingScrollTop: currentScrollTop,
                  //   currentScrollPositions: { ...scrollPositionsRef.current }
                  // });

                  if (variableListContainerRef.current && currentKey) {
                    scrollPositionsRef.current[currentKey] = currentScrollTop;
                  }

                  // Update current level id after selection (used by onScroll)
                  const newLevelId = typeof itemId === 'string' ? itemId : String(itemId);
                  currentLevelIdRef.current = newLevelId;

                  setSelectedItems(itemId)
                }}
                slots={{
                  item: CustomTreeItem
                }}
                slotProps={{
                  item: (ownerState: any) => {
                    // Find the tree item data for this node
                    const findTreeItem = (items: TreeItemData[], id: string): TreeItemData | null => {
                      for (const item of items) {
                        if (item.id === id) return item;
                        if (item.children) {
                          const found = findTreeItem(item.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };

                    const treeItem = findTreeItem(treeItems, ownerState.itemId);
                    // Compute current level index at this tree item for constraint filtering
                    const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                      for (const n of nodes) {
                        if (n.id === id) return n;
                        if (n.children) {
                          const f = findNodeByIdForLevel(n.children, id);
                          if (f) return f;
                        }
                      }
                      return null;
                    };
                    const currentLevelIndexForTreeItem = (() => {
                      if (!treeItem) return undefined;
                      if (treeItem.type === 'global') return 0;
                      if (treeItem.type === 'dataset') return 999;
                      const cat = treeItem.nodeId ? findNodeByIdForLevel(data?.tree || [], treeItem.nodeId) : null;
                      return cat?.level ?? 1;
                    })();

                    // Start from variables defined directly in this node
                    let nodeVariables = treeItem ? getVariablesDefinedInNode(treeItem.nodeId, treeItem.datasetId) : [];

                    // Filter out variables hidden by minLevel/maxLevel constraints so focus dots match visibility
                    if (treeItem && currentLevelIndexForTreeItem !== undefined) {
                      const filterByConstraints = (variableName: string): boolean => {
                        if (!data) return true;
                        let variable: any | undefined;
                        if (treeItem.type === 'global') {
                          variable = (data.template_variables || []).find((v: any) => v.name === variableName);
                        } else if (treeItem.type === 'category') {
                          const cat = findNodeByIdForLevel(data.tree || [], treeItem.nodeId!);
                          variable = cat?.variables?.find((v: any) => v.name === variableName);
                        } else if (treeItem.type === 'dataset') {
                          const cat = findNodeByIdForLevel(data.tree || [], treeItem.nodeId!);
                          const ds = cat?.datasets?.find((d: any) => d.id === treeItem.datasetId);
                          variable = ds?.variables?.find((v: any) => v.name === variableName);
                        }
                        if (!variable) return false;
                        const constraints = variable.meta?.constraints || variable.gui?.constraints;
                        if (!constraints) return true;
                        const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
                        const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
                        return currentLevelIndexForTreeItem >= min && currentLevelIndexForTreeItem <= max;
                      };
                      nodeVariables = nodeVariables.filter(filterByConstraints);
                    }

                    return {
                      focusedVariable,
                      nodeVariables,
                      getVariableState: getVariableStateForContext,
                      nodeId: treeItem?.nodeId,
                      datasetId: treeItem?.datasetId,
                      flash: ownerState.itemId === 'global-root' ? flashGlobal : false
                    } as any; // Type assertion to bypass strict typing
                  }
                }}
                sx={{
                  flexGrow: 1,
                  maxWidth: '100%',
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                No categories found
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Settings */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper sx={{ p: 2, height: 'calc(100vh - 240px)', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="subtitle1">
                Settings
              </Typography>
              {selectedNode && (
                <Chip
                  label={selectedNode.type === 'global' ? 'Global' :
                         selectedNode.type === 'dataset' ? 'Dataset' : 'Category'}
                  size="small"
                  color={selectedNode.type === 'global' ? 'primary' :
                         selectedNode.type === 'dataset' ? 'secondary' : 'default'}
                  variant="outlined"
                />
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              🟢 Active • 🟠 Overridden • 🔘 Inherited from higher level • 🔴 Not set
            </Typography>
            {selectedNode ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: 0, flexGrow: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' :
                   selectedNode.type === 'global' ? 'Global' : 'Category'}: {selectedNode.label}
                </Typography>
                {(() => {
                  // Get all available variables for this context (including inherited)
                  const nodeId = selectedNode.type === 'global' ? undefined : selectedNode.nodeId;
                  const datasetId = selectedNode.type === 'global' ? undefined : selectedNode.datasetId;


                  const allVariableNames = selectedNode.type === 'global' ?
                    (data?.template_variables || []).map((v: any) => v.name) :
                    getAllVariablesForContext(nodeId, datasetId);

                  return allVariableNames.length > 0 ? (
                    <Box
                      ref={variableListContainerRef}
                      onScroll={(e) => {
                        // Don't save scroll position if we're currently restoring it
                        if (isRestoringScrollRef.current) {
                          // Debug: Ignoring scroll event during restoration
                          // console.log('🚫 Ignoring scroll event during restoration:', {
                          //   scrollTop: (e.currentTarget as HTMLDivElement).scrollTop,
                          //   currentLevelId: currentLevelIdRef.current
                          // });
                          return;
                        }

                        // Persist scroll under the current level id (stable across transitions)
                        const key = currentLevelIdRef.current || getLevelKey(selectedNode);
                        const scrollTop = (e.currentTarget as HTMLDivElement).scrollTop;

                        // Debug: Saving scroll event
                        // console.log('📜 Saving scroll event:', {
                        //   levelKey: key,
                        //   scrollTop: scrollTop,
                        //   currentLevelId: currentLevelIdRef.current,
                        //   isRestoring: isRestoringScrollRef.current,
                        //   previousValue: scrollPositionsRef.current[key]
                        // });

                        if (key) {
                          scrollPositionsRef.current[key] = scrollTop;
                        }
                      }}
                      sx={{
                        display: 'block',
                        flexGrow: 1,
                        overflowY: 'auto',
                        p: 1,
                        minHeight: 0
                      }}
                    >
                      {showInputs && (() => {
                        // Determine the current hierarchical level index for constraint filtering
                        const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                          for (const n of nodes) {
                            if (n.id === id) return n;
                            if (n.children) {
                              const f = findNodeByIdForLevel(n.children, id);
                              if (f) return f;
                            }
                          }
                          return null;
                        };
                        const currentLevelIndex = (() => {
                          if (selectedNode.type === 'global') return 0;
                          if (selectedNode.type === 'dataset') return 999;
                          // category: look up level from data.tree (backend sets level: 1+)
                          const cat = nodeId ? findNodeByIdForLevel(data?.tree || [], nodeId) : null;
                          return cat?.level ?? 1;
                        })();

                        const items = allVariableNames
                          .map(variableName => {
                            let variable;
                            if (selectedNode.type === 'global') {
                              variable = (data?.template_variables || []).find((v: any) => v.name === variableName);
                            } else {
                              const allVariables = getVariablesByName(variableName);
                               variable = allVariables.find((v: any) =>
                                (nodeId && v.source_level === 'Category') ||
                                (datasetId && v.source_level === 'Dataset') ||
                                v.source_level === 'Template'
                              );
                            }                              return { name: variableName, variable };
                          })
                          // Filter out variables without components
                          .filter(({ variable }) => variable?.gui?.component_id)
                          // Enforce minLevel/maxLevel visibility: hide variables outside constraints
                          .filter(({ variable }) => {
                            const constraints = variable?.meta?.constraints || variable?.gui?.constraints;
                            if (!constraints) return true; // no constraints → visible everywhere
                            const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
                            const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
                            return currentLevelIndex >= min && currentLevelIndex <= max;
                          });

                        const groupsMap = new Map<string, any[]>();
                        for (const item of items) {
                          const g = item.variable?.gui?.group || 'Variable Tree';
                          if (!groupsMap.has(g)) groupsMap.set(g, []);
                          groupsMap.get(g)!.push(item.variable);
                        }

                        const sortedGroups = Array.from(groupsMap.entries()).sort(([ga, va], [gb, vb]) => {
                          const minA = Math.min(...va.map(v => v.gui?.order ?? Number.POSITIVE_INFINITY));
                          const minB = Math.min(...vb.map(v => v.gui?.order ?? Number.POSITIVE_INFINITY));
                          return minA - minB;
                        });

                        return (
                          <Box>
                            {sortedGroups.map(([groupName, vars]) => (
                              <VariableTreeGroupRenderer
                                key={groupName}
                                groupName={groupName}
                                variables={vars}
                                renderVariable={(variable) => {
                                  const state = selectedNode.type === 'global'
                                    ? getVariableState(variable.name)
                                    : getVariableStateForContext(variable.name, nodeId, datasetId);

                                  return (
                                    <VariableInputRenderer
                                      variable={variable}
                                      onChange={(variableName: string, newValue: any) => {
                                        if (selectedNode.type === 'global') {
                                          updateVariable(variableName, newValue, variable);
                                        } else {
                                          const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                          updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                        }
                                      }}
                                      template_data={data}
                                      contextInfo={{
                                        nodeId,
                                        nodeType: selectedNode.type === 'global' ? 'category' : selectedNode.type,
                                        nodeName: selectedNode.label
                                      }}
                                      showStatusBadge={true}
                                      variableState={state}
                                      currentValue={(function() {
                                        const isResetPending = selectedNode.type === 'global'
                                          ? isVariableResetPending(variable.name)
                                          : isVariableResetPending(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                        const primary = state.primaryState;
                                        const findNodeById = (nodes: any[], id: number): any | null => {
                                          for (const n of nodes) {
                                            if (n.id === id) return n;
                                            if (n.children) {
                                              const f = findNodeById(n.children, id);
                                              if (f) return f;
                                            }
                                          }
                                          return null;
                                        };
                                        const cat = nodeId ? findNodeById(data?.tree || [], nodeId) : null;
                                        const contextVar = (function() {
                                          if (selectedNode.type === 'global') {
                                            return (data?.template_variables || []).find((v: any) => v.name === variable.name);
                                          }
                                          if (selectedNode.type === 'category') {
                                            return cat?.variables?.find((v: any) => v.name === variable.name);
                                          } else if (selectedNode.type === 'dataset') {
                                            const ds = cat?.datasets?.find((d: any) => d.id === datasetId);
                                            return ds?.variables?.find((v: any) => v.name === variable.name);
                                          }
                                          return undefined;
                                        })();

                                        const inherited = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                        let originalDisplayValue: any;
                                        if (isResetPending) {
                                          originalDisplayValue = inherited;
                                        } else if (primary === 'defined-higher') {
                                          originalDisplayValue = inherited;
                                        } else if (primary === 'active' || primary === 'overridden') {
                                          const val = contextVar?.value ?? contextVar?.data;
                                          originalDisplayValue = (val !== undefined) ? val : inherited;
                                        } else {
                                          originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                        }
                                        return selectedNode.type === 'global'
                                          ? getVariableValue(variable.name, originalDisplayValue)
                                          : getVariableValue(
                                              variable.name,
                                              originalDisplayValue,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                      })()}
                                      pendingOverride={
                                        (selectedNode.type === 'global'
                                          ? isVariableChanged(variable.name)
                                          : isVariableChanged(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            )) && state.primaryState === 'defined-higher'
                                      }
                                      pendingReset={
                                        selectedNode.type === 'global'
                                          ? isVariableResetPending(variable.name)
                                          : isVariableResetPending(
                                              variable.name,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            )
                                      }
                                      focusVariable={focusVariable}
                                      unfocusVariable={unfocusVariable}
                                      isVariableFocused={isVariableFocused}
                                      onOverride={(variableName) => {
                                        const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                        const nodeType = getContextNodeType(selectedNode);
                                        const currentValue = selectedNode.type === 'global'
                                          ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                          : getVariableValue(
                                              variableName,
                                              state.activeVariable?.variable?.data,
                                              selectedNode.type === 'dataset' ? datasetId : nodeId,
                                              getContextNodeType(selectedNode)
                                            );
                                        updateVariable(variableName, currentValue, variable!, contextId, nodeType);
                                      }}
                                      onReset={selectedNode.type === 'global' ? undefined : (variableName) => {
                                        const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                        const nodeType = selectedNode.type as 'category' | 'dataset';
                                        const inherited = getInheritedValueForContext(variableName, nodeId, datasetId);
                                        resetToInherited(variableName, contextId, nodeType, inherited);
                                      }}
                                      onGoToDefining={navigateToDefiningLevel}
                                    />
                                  );
                                }}
                              />
                            ))}
                          </Box>
                        );
                      })()}
                      {!showInputs && (() => {
                        // Compute current level index for constraint filtering
                        const findNodeByIdForLevel = (nodes: any[], id: number): any | null => {
                          for (const n of nodes) {
                            if (n.id === id) return n;
                            if (n.children) {
                              const f = findNodeByIdForLevel(n.children, id);
                              if (f) return f;
                            }
                          }
                          return null;
                        };
                        const currentLevelIndex = (() => {
                          if (selectedNode.type === 'global') return 0;
                          if (selectedNode.type === 'dataset') return 999;
                          const cat = nodeId ? findNodeByIdForLevel(data?.tree || [], nodeId) : null;
                          return cat?.level ?? 1;
                        })();

                        return allVariableNames
                        .map(variableName => {
                          let variable;
                          if (selectedNode.type === 'global') {
                            // For global context, get template variables directly
                            variable = (data?.template_variables || []).find((v: any) => v.name === variableName);
                          } else {
                            // For category/dataset context, use existing logic
                            const allVariables = getVariablesByName(variableName);
                            variable = allVariables.find((v: any) =>
                              (nodeId && v.source_level === 'Category') ||
                              (datasetId && v.source_level === 'Dataset') ||
                              v.source_level === 'Template'
                            );
                          }

                          // Debug logging for GNSSES variable
                          if (variableName === 'GNSSES') {
                            console.log('GNSSES variable debug:', {
                              variableName,
                              selectedVariable: variable,
                              nodeId,
                              datasetId,
                              selectedNodeType: selectedNode.type,
                              hasGui: variable?.gui?.component_id
                            });
                          }

                          const state = selectedNode.type === 'global' ?
                            getVariableState(variableName) :
                            getVariableStateForContext(variableName, nodeId, datasetId);



                          return {
                            name: variableName,
                            state,
                            variable
                          };
                        })
                        .filter(({ variable }) => {
                          // In INPUT-KOMPONENTEN mode: only show variables with GUI components
                          // In STATUS-BADGES mode: show all variables
                          return showInputs ? variable?.gui?.component_id : true;
                        })
                        // Hide variables outside minLevel/maxLevel constraints in STATUS view
                        .filter(({ variable }) => {
                          const constraints = variable?.meta?.constraints || variable?.gui?.constraints;
                          if (!constraints) return true;
                          const min = typeof constraints.minLevel === 'number' ? constraints.minLevel : 0;
                          const max = typeof constraints.maxLevel === 'number' ? constraints.maxLevel : 999;
                          return currentLevelIndex >= min && currentLevelIndex <= max;
                        })
                        .sort((a, b) => {
                          // Sort by state priority: active > overridden > defined-higher > not-set
                          const stateOrder = { 'active': 0, 'overridden': 1, 'defined-higher': 2, 'not-set': 3 };
                          const aOrder = stateOrder[a.state.primaryState] || 3;
                          const bOrder = stateOrder[b.state.primaryState] || 3;
                          if (aOrder !== bOrder) return aOrder - bOrder;
                          // If same state, sort alphabetically
                          return a.name.localeCompare(b.name);
                        })
                        .map(({ name, state, variable }, index) => {
                          if (showInputs && variable?.gui?.component_id) {
                            return (
                              <VariableInputRenderer
                                key={index}
                                variable={variable}
                                onChange={(variableName: string, newValue: any) => {
                                  // Handle different node types for variable updates
                                  if (selectedNode.type === 'global') {
                                    // Global variables are template variables (no context ID needed)
                                    updateVariable(variableName, newValue, variable);
                                  } else {
                                    // Use datasetId for datasets, nodeId for categories
                                    const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                    updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
                                  }
                                }}
                                template_data={data}
                                contextInfo={{
                                  nodeId,
                                  nodeType: selectedNode.type === 'global' ? 'category' : selectedNode.type,
                                  nodeName: selectedNode.label
                                }}
                                showStatusBadge={true}
                                variableState={state}
                                currentValue={(function() {
                                  // Determine which value to display based on status and context
                                  const isResetPending = selectedNode.type === 'global'
                                    ? isVariableResetPending(variable.name)
                                    : isVariableResetPending(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                  const primary = state.primaryState;
                                  // Helper: find the variable defined at the current context level
                                  const findContextVariable = () => {
                                    if (selectedNode.type === 'global') {
                                      return (data?.template_variables || []).find((v: any) => v.name === variable.name);
                                    }
                                    // Find the category node by nodeId
                                    const findNodeById = (nodes: any[], id: number): any | null => {
                                      for (const n of nodes) {
                                        if (n.id === id) return n;
                                        if (n.children) {
                                          const f = findNodeById(n.children, id);
                                          if (f) return f;
                                        }
                                      }
                                      return null;
                                    };
                                    const cat = nodeId ? findNodeById(data?.tree || [], nodeId) : null;
                                    if (selectedNode.type === 'category') {
                                      return cat?.variables?.find((v: any) => v.name === variable.name);
                                    } else if (selectedNode.type === 'dataset') {
                                      const ds = cat?.datasets?.find((d: any) => d.id === datasetId);
                                      return ds?.variables?.find((v: any) => v.name === variable.name);
                                    }
                                    return undefined;
                                  };
                                  const contextVar = findContextVariable();
                                  let originalDisplayValue: any;
                                  if (isResetPending || primary === 'defined-higher') {
                                    // Show inherited value from higher level
                                    originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                  } else if (primary === 'active' || primary === 'overridden') {
                                    // Show the value actually set at the current level
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                    // Fallback to inherited if not found for safety
                                    if (originalDisplayValue === undefined) {
                                      originalDisplayValue = getInheritedValueForContext(variable.name, nodeId, datasetId);
                                    }
                                  } else {
                                    // not-set or other: show undefined/inherited
                                    originalDisplayValue = contextVar?.value ?? contextVar?.data;
                                  }
                                  return selectedNode.type === 'global'
                                    ? getVariableValue(variable.name, originalDisplayValue)
                                    : getVariableValue(
                                        variable.name,
                                        originalDisplayValue,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                })()}
                                pendingReset={
                                  selectedNode.type === 'global'
                                    ? isVariableResetPending(variable.name)
                                    : isVariableResetPending(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      )
                                }
                                pendingOverride={
                                  (selectedNode.type === 'global'
                                    ? isVariableChanged(variable.name)
                                    : isVariableChanged(
                                        variable.name,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      )) && state.primaryState === 'defined-higher'
                                }
                                focusVariable={focusVariable}
                                unfocusVariable={unfocusVariable}
                                isVariableFocused={isVariableFocused}
                                onReset={selectedNode.type === 'global' ? undefined : (variableName) => {
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = selectedNode.type as 'category' | 'dataset';
                                  const inherited = getInheritedValueForContext(variableName, nodeId, datasetId);
                                  // LOCAL reset: mark pending reset and restore inherited visuals
                                  resetToInherited(variableName, contextId, nodeType, inherited);
                                }}
                                onOverride={(variableName) => {
                                  // Create an override at the current level
                                  const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                  const nodeType = getContextNodeType(selectedNode);
                                  // Get the current active value to use as initial override value
                                  const currentValue = selectedNode.type === 'global'
                                    ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                    : getVariableValue(
                                        variableName,
                                        state.activeVariable?.variable?.data,
                                        selectedNode.type === 'dataset' ? datasetId : nodeId,
                                        getContextNodeType(selectedNode)
                                      );
                                  updateVariable(variableName, currentValue, variable!, contextId, nodeType);
                                }}
                                onGoToDefining={navigateToDefiningLevel}
                              />
                            );
                          } else {
                            return (
                              <Box
                                key={index}
                                sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}
                                data-variable-name={name}
                              >
                                <Box
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (isVariableFocused(name)) {
                                      unfocusVariable();
                                    } else {
                                      focusVariable(name);
                                    }
                                  }}
                                  sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                                >
                                  <VariableStatusBadge
                                    variableName={name}
                                    primaryState={
                                      (selectedNode.type === 'global'
                                        ? isVariableResetPending(name)
                                        : isVariableResetPending(
                                            name,
                                            selectedNode.type === 'dataset' ? datasetId : nodeId,
                                            getContextNodeType(selectedNode)
                                          ))
                                    ? 'defined-higher'
                                    : ((selectedNode.type === 'global'
                                        ? isVariableChanged(name)
                                        : isVariableChanged(
                                            name,
                                            selectedNode.type === 'dataset' ? datasetId : nodeId,
                                            getContextNodeType(selectedNode)
                                          )) && state.primaryState === 'defined-higher')
                                      ? 'active'
                                      : state.primaryState
                                  }
                                    secondaryState={state.secondaryState}
                                    counts={state.counts}
                                    activeVariable={state.activeVariable}
                                    overriddenBy={state.overriddenBy}
                                    showActions={true}
                                    onGoToDefining={navigateToDefiningLevel}
                                    onOverride={(variableName) => {
                                      // Create an override at the current level
                                      const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
                                      const nodeType = getContextNodeType(selectedNode);
                                      // Get the current active value to use as initial override value
                                      const currentValue = selectedNode.type === 'global'
                                        ? getVariableValue(variableName, state.activeVariable?.variable?.data)
                                        : getVariableValue(
                                            variableName,
                                            state.activeVariable?.variable?.data,
                                            selectedNode.type === 'dataset' ? datasetId : nodeId,
                                            getContextNodeType(selectedNode)
                                          );
                                      updateVariable(variableName, currentValue, variable!, contextId, nodeType);
                                    }}
                                    currentPath={selectedNode.type === 'global' ? 'Global' :
                                      selectedNode.type === 'dataset' ? `Global → ${selectedNode.label.split(' (')[0]} → ${selectedNode.label}` :
                                      `Global → ${selectedNode.label.split(' (')[0]}`}
                                  />
                                  <VariableFocusIndicator
                                    isVisible={isVariableFocused(name)}
                                    primaryState={state.primaryState}
                                  />
                                </Box>
                              </Box>
                            );
                          }
                        })
                      })()}
                    </Box>
                  ) : (
                    <Alert severity="info">
                      No settings available for this {selectedNode.type === 'dataset' ? 'dataset' :
                        selectedNode.type === 'global' ? 'global context' : 'category'}
                    </Alert>
                  );
                })()}
              </Box>
            ) : (
              <Alert severity="info">
                Select a node from the tree to view and configure settings
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Save Success Message */}
      {saveSuccess && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Changes saved successfully
        </Alert>
      )}

      {/* Error Snackbar */}
      <Snackbar
        open={showErrorSnackbar}
        autoHideDuration={6000}
        onClose={() => setShowErrorSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowErrorSnackbar(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          Error saving: {saveError}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default VariableTreeView;
