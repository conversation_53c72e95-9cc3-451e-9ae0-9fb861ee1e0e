import React from 'react';
import { Chip, Tooltip, Box, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import { getVariableStateColor, getVariableStateLabel, type VariableState } from '../../lib/utils/variableColors';
import EditIcon from '@mui/icons-material/Edit';
import LaunchIcon from '@mui/icons-material/Launch';

interface VariableStatusBadgeProps {
  variableName: string;
  primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
  secondaryState?: 'active' | 'overridden' | 'not-set';
  counts: {
    active: number;
    overridden: number;
    notSet: number;
    definedHigher: number;
    total: number;
  };
  activeVariable?: any;
  overriddenBy?: string[];
  size?: 'small' | 'medium';
  showTooltip?: boolean;
  // Action handlers
  onOverride?: (variableName: string) => void;
  onGoToDefining?: (variableName: string) => void;
  showActions?: boolean;
  // Enhanced path information
  currentPath?: string; // Current context path (e.g., "Global → Category1 → Dataset1")
}

const StyledChip = styled(Chip)<{ 
  primarycolor: string; 
  secondarycolor?: string;
  hassecondary: boolean;
}>(({ theme, primarycolor, secondarycolor, hassecondary }) => ({
  position: 'relative',
  backgroundColor: primarycolor,
  color: theme.palette.getContrastText(primarycolor),
  '&::after': hassecondary ? {
    content: '""',
    position: 'absolute',
    top: 2,
    right: 2,
    width: 6,
    height: 6,
    borderRadius: '50%',
    backgroundColor: secondarycolor,
    border: `1px solid ${theme.palette.background.paper}`,
  } : {},
  '&:hover': {
    backgroundColor: primarycolor,
    filter: 'brightness(0.9)',
  },
}));

// Color and label functions are now imported from utils

const generateTooltipContent = (
  variableName: string,
  primaryState: string,
  counts: VariableStatusBadgeProps['counts'],
  activeVariable?: any,
  overriddenBy?: string[],
  currentPath?: string
): string => {
  const { active, overridden, notSet, definedHigher, total } = counts;

  let content = `Variable: ${variableName}\n`;
  content += `Status: ${getVariableStateLabel(primaryState as any)}\n`;

  // Add current context path
  if (currentPath) {
    content += `Current Context: ${currentPath}\n`;
  }
  content += `\n`;

  if (activeVariable) {
    content += `🟢 Active Variable:\n`;
    // Enhanced path display with → separators
    const enhancedPath = activeVariable.path ?
      activeVariable.path.replace(/\s*>\s*/g, ' → ') :
      'Unknown path';
    content += `   Defined at: ${enhancedPath}\n`;
    content += `   Value: ${JSON.stringify(activeVariable.variable.data)}\n`;
    if (primaryState === 'defined-higher') {
      content += `   (Inherited from higher level)\n`;
    }
    content += `\n`;
  }

  if (overriddenBy && overriddenBy.length > 0) {
    content += `🟠 Overridden by:\n`;
    overriddenBy.forEach(path => {
      content += `   • ${path}\n`;
    });
    content += `\n`;
  }

  content += `Summary (${total} instances):\n`;
  if (active > 0) content += `• Active: ${active}\n`;
  if (overridden > 0) content += `• Overridden: ${overridden}\n`;
  if (definedHigher > 0) content += `• Higher level: ${definedHigher}\n`;
  if (notSet > 0) content += `• Not set: ${notSet}\n`;
  
  return content;
};

export const VariableStatusBadge: React.FC<VariableStatusBadgeProps> = ({
  variableName,
  primaryState,
  secondaryState,
  counts,
  activeVariable,
  overriddenBy,
  size = 'small',
  showTooltip = true,
  onOverride,
  onGoToDefining,
  showActions = false,
  currentPath
}) => {
  const primaryColor = getVariableStateColor(primaryState);
  const secondaryColor = secondaryState ? getVariableStateColor(secondaryState) : undefined;
  const hasSecondary = Boolean(secondaryState);
  
  const badge = (
    <StyledChip
      label={variableName}
      size={size}
      primarycolor={primaryColor}
      secondarycolor={secondaryColor}
      hassecondary={hasSecondary}
      variant="filled"
    />
  );



  const badgeWithActions = showActions ? (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
      {badge}
      {/* Override/Specify action - show for inherited variables */}
      {primaryState === 'defined-higher' && onOverride && (
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onOverride(variableName);
          }}
          sx={{
            width: 20,
            height: 20,
            color: 'action.secondary',
            '&:hover': { color: 'primary.main' }
          }}
          title="Override at this level"
        >
          <EditIcon sx={{ fontSize: 14 }} />
        </IconButton>
      )}
      {/* Go to defining level action - show only for gray "inherited" status */}
      {primaryState === 'defined-higher' && activeVariable && onGoToDefining && (
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onGoToDefining(variableName);
          }}
          sx={{
            width: 20,
            height: 20,
            color: 'action.secondary',
            '&:hover': { color: 'primary.main' }
          }}
          title="Go to defining level"
        >
          <LaunchIcon sx={{ fontSize: 14 }} />
        </IconButton>
      )}
    </Box>
  ) : badge;

  if (!showTooltip) {
    return badgeWithActions;
  }

  return (
    <Tooltip
      title={
        <Box component="pre" sx={{
          whiteSpace: 'pre-line',
          fontSize: '0.75rem',
          fontFamily: 'monospace'
        }}>
          {generateTooltipContent(variableName, primaryState, counts, activeVariable, overriddenBy, currentPath)}
        </Box>
      }
      placement="top"
      arrow
    >
      {badgeWithActions}
    </Tooltip>
  );
};

export default VariableStatusBadge;