import React from 'react';
import { TreeItem, TreeItemProps } from '@mui/x-tree-view/TreeItem';
import { Box } from '@mui/material';
import { VariableFocusIndicator } from './VariableFocusIndicator';
import { type VariableState } from '../../lib/utils/variableColors';

interface CustomTreeItemProps extends TreeItemProps {
  // Props for focus indicator
  focusedVariable?: string | null;
  nodeVariables?: string[]; // Variables defined in this node
  getVariableState?: (variableName: string, nodeId?: number, datasetId?: number) => {
    primaryState: VariableState;
    secondaryState?: VariableState;
    counts?: any;
    activeVariable?: any;
    overriddenBy?: string;
  };
  nodeId?: number;
  datasetId?: number;
  // Optional flash highlight for attention (e.g., after template change)
  flash?: boolean;
}

export const CustomTreeItem: React.FC<CustomTreeItemProps> = ({
  focusedVariable,
  nodeVariables = [],
  getVariableState,
  nodeId,
  datasetId,
  label,
  flash = false,
  ...otherProps
}) => {
  // Check if the focused variable is defined in this node
  const shouldShowIndicator = focusedVariable && nodeVariables.includes(focusedVariable);

  // Get the variable state for the focused variable if it's in this node
  const variableState = shouldShowIndicator && getVariableState
    ? getVariableState(focusedVariable, nodeId, datasetId)
    : null;

  // Create enhanced label with focus indicator
  const enhancedLabel = (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        borderRadius: 1,
        transition: 'background-color 800ms ease, box-shadow 800ms ease',
        ...(flash
          ? {
              backgroundColor: 'rgba(25, 118, 210, 0.12)',
              boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.18) inset'
            }
          : {})
      }}
    >
      <span>{label}</span>
      {shouldShowIndicator && variableState && (
        <VariableFocusIndicator
          isVisible={true}
          primaryState={variableState.primaryState}
          size={12}
        />
      )}
    </Box>
  );

  return (
    <TreeItem
      label={enhancedLabel}
      {...otherProps}
    />
  );
};

export default CustomTreeItem;
