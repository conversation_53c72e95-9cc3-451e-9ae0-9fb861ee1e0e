// components/inputs/LogSettingEccFRD.tsx
import { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogSettingEccFRDProps {
  value: number[];
  onChange: (value: number[]) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    min_value?: number;
    max_value?: number;
    max_digits?: number;
    unit?: string;
    [key: string]: any;
  };
}

export default function LogSettingEccFRD({
  value = [0, 0, 0],
  onChange,
  name,
  gui,
}: LogSettingEccFRDProps) {
  // Ensure we have exactly 3 values for Front, Right, Down
  const initialValues = value.length === 3 ? value : [0, 0, 0];
  const [localValues, setLocalValues] = useState<number[]>(initialValues);

  useEffect(() => {
    if (value.length === 3) {
      setLocalValues(value);
    }
  }, [value]);

  const handleChange = (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value);
    const newValues = [...localValues];
    newValues[index] = isNaN(newValue) ? 0 : newValue;
    setLocalValues(newValues);
    // Update parent component immediately to ensure changes are tracked
    onChange(newValues);
  };

  // Handle blur event to update parent only when focus leaves the field
  const handleBlur = () => {
    onChange(localValues);
  };

  const labels = ['Front', 'Right', 'Down'];
  const displayStep = gui.max_digits
    ? Math.pow(10, -gui.max_digits).toString()
    : 'any';

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label || 'Excentricity/Lever Arm (Front, Right, Down)'}
            {gui.unit && ` [${gui.unit || 'm'}]`}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}

      <Stack direction="row" spacing={2}>
        {localValues.map((val, index) => (
          <TextField
            key={index}
            label={labels[index]}
            size="small"
            type="number"
            value={val}
            onChange={handleChange(index)}
            onBlur={handleBlur}
            inputProps={{
              step: displayStep,
              min: gui.min_value,
              max: gui.max_value,
            }}
            sx={{ flex: 1 }}
          />
        ))}
      </Stack>
    </FormControl>
  );
}
