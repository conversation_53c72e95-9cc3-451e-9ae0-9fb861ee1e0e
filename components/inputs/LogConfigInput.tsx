// components/inputs/LogConfigInput.tsx
import React, {
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  LogConfigInputProps,
  LogField,
  LogFieldSetting,
  DatabaseLogField,
  LogGroup,
  TriggerSettings,
  CsvSettings,
  LogConfigValue,
  EditingLogField,
  LogDatabase,
  FilteredLogGroup,
  LogFieldChangeHandler,
  LogFieldEditHandler,
  LogFieldSaveHandler,
  LogFieldRemoveHandler,
  CsvSettingsChangeHandler,
  TriggerChangeHandler,
  SettingChangeHandler,
} from './types/LogConfigTypes';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box,
  Paper,
  Typography,
  Divider,
} from '@mui/material';
import { useLogFields } from '@/lib/hooks/useLogFields';
import { usePerformanceMonitor, useBundleAnalyzer } from '@/lib/hooks/usePerformanceMonitor';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

import LogSettingCols from './LogSettingCols';
import LogSettingColsPS from './LogSettingColsPS';
import LogSettingEccFRD from './LogSettingEccFRD';
import LogSettingAsciiFormat from './LogSettingAsciiFormat';
import LogSettingCsvSettings from './LogSettingCsvSettings';

// Import new sub-components
import LogConfigTriggerSettings from './logconfig/LogConfigTriggerSettings';
import LogConfigCsvSettings from './logconfig/LogConfigCsvSettings';
import LogConfigFieldSelector from './logconfig/LogConfigFieldSelector';
import LogConfigFieldEditorLazy from './logconfig/LogConfigFieldEditorLazy';
import { useLogConfigReducer } from './logconfig/useLogConfigReducer';



export default function LogConfigInput({
  value = {
    label: '',
    trigger: 'RTS_TIME' as const,
    dt: 0.1,
    sensor: '',
    step: 1,
    fields: [],
    csv_settings: {}
  },
  onChange,
  name,
  gui,
  template_data,
}: LogConfigInputProps) {
  /** ----------------------- Performance Monitoring ----------------------- */
  usePerformanceMonitor('LogConfigInput');
  useBundleAnalyzer('LogConfigInput', [
    '@mui/material',
    'react-beautiful-dnd',
    'LogConfigTriggerSettings',
    'LogConfigCsvSettings',
    'LogConfigFieldSelector',
    'LogConfigFieldEditorLazy'
  ]);

  /** ------------------------- State mit useReducer ------------------------ */
  const initialTriggerSettings: TriggerSettings = {
    trigger: value.trigger || 'RTS_TIME',
    dt: value.trigger === 'RTS_TIME' ? value.dt || 0.1 : undefined,
    sensor: value.trigger === 'RTS_MEAS' ? value.sensor || '' : undefined,
    step: (value.trigger === 'RTS_MEAS' || value.trigger === 'RTS_PREDICTOR') ? value.step || 1 : undefined,
  };

  const initialCsvSettings: CsvSettings = value.csv_settings || {
    delim: ", ",
    delim_sub: ",",
    eol: "\n",
    delimiter_after_last_col: false,
    output_full_header: true,
    output_header_row: true,
    header_prefix: "# ",
  };

  const { state, dispatch, actions } = useLogConfigReducer(
    value.label || '',
    initialTriggerSettings,
    value.fields || [],
    initialCsvSettings
  );

  const {
    label,
    triggerSettings,
    fields,
    csvSettings,
    searchTerm,
    expandedGroups,
    editingField,
    modalOpen,
    logDatabase,
  } = state;

  /** ---------------- Gegen Endlosschleifen sichern ----------------- */
  const isUpdating = useRef(false);
  const prevValueRef = useRef(value);

  /** --------------- Kombinierten Wert an Parent melden ------------- */
  const combinedValue = useMemo(
    () => ({ label, ...triggerSettings, fields, csv_settings: csvSettings }),
    [label, triggerSettings, fields, csvSettings],
  );

  useEffect(() => {
    if (isUpdating.current) return;
    if (JSON.stringify(prevValueRef.current) !== JSON.stringify(combinedValue)) {
      isUpdating.current = true;
      onChange(combinedValue);
      prevValueRef.current = combinedValue;
      setTimeout(() => (isUpdating.current = false), 0);
    }
  }, [combinedValue, onChange]);

  /** -------- Props-Update → lokalen State synchronisieren ---------- */
  useEffect(() => {
    if (isUpdating.current) return;
    
    const newTriggerSettings: TriggerSettings = {
      trigger: value.trigger || 'RTS_TIME',
      dt: value.trigger === 'RTS_TIME' ? value.dt || 0.1 : undefined,
      sensor: value.trigger === 'RTS_MEAS' ? value.sensor || '' : undefined,
      step: (value.trigger === 'RTS_MEAS' || value.trigger === 'RTS_PREDICTOR') ? value.step || 1 : undefined,
    };

    const newCsvSettings: CsvSettings = value.csv_settings || {
      delim: ", ",
      delim_sub: ",",
      eol: "\n",
      delimiter_after_last_col: false,
      output_full_header: true,
      output_header_row: true,
      header_prefix: "# ",
    };

    // Only update if the values have actually changed
    const hasChanged =
      label !== (value.label || '') ||
      JSON.stringify(triggerSettings) !== JSON.stringify(newTriggerSettings) ||
      JSON.stringify(fields) !== JSON.stringify(value.fields || []) ||
      JSON.stringify(csvSettings) !== JSON.stringify(newCsvSettings);

    if (hasChanged) {
      dispatch({
        type: 'RESET_STATE',
        payload: {
          label: value.label || '',
          triggerSettings: newTriggerSettings,
          fields: value.fields || [],
          csvSettings: newCsvSettings,
        }
      });
    }
  }, [value, label, triggerSettings, fields, csvSettings, dispatch]);

  /** ------------------------ Log-Datenbank ------------------------- */
  // Verwenden des useLogFields-Hooks für die Datenbankabfrage
  const { data: logFieldsData, isLoading, error } = useLogFields();

  // Aktualisieren des lokalen Zustands, wenn Daten vom Hook geladen werden
  useEffect(() => {
    if (logFieldsData) {
      dispatch({ type: 'SET_LOG_DATABASE', payload: logFieldsData });
    }
  }, [logFieldsData, dispatch]);

  // Fallback für Fehler oder wenn keine Daten verfügbar sind
  useEffect(() => {
    if (error || (!isLoading && !logFieldsData)) {
      console.error('Error loading log fields:', error);

      // Keine Mock-Daten mehr verwenden, stattdessen Error-State zeigen
      console.warn('No log fields data available. Please ensure the database connection is working.');
    }
  }, [error, isLoading, logFieldsData]);

  /** ---------------------- Event-Handler usw. ---------------------- */
  const handleLabelChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => actions.updateLabel(e.target.value),
    [actions],
  );

  const handleCsvSettingsChange = useCallback(
    (newSettings: CsvSettings) => actions.updateCsvSettings(newSettings),
    [actions],
  );

  const handleAddField = useCallback(
    (field: DatabaseLogField, groupName: string, logprefix: string, index?: number) => {
      console.log('Adding field:', field, 'from group:', groupName, 'with logprefix:', logprefix);
      actions.addField(field, groupName, logprefix, index);
    },
    [actions],
  );

  const handleRemoveField = useCallback(
    (idx: number) => actions.removeField(idx),
    [actions],
  );

  const handleEditField = useCallback((f: LogField, index: number) => {
    console.log('Editing field:', f, 'at index:', index);
    actions.editField(f, index);
  }, [actions]);

  const handleCancelFieldEdit = useCallback(() => {
    actions.cancelFieldEdit();
  }, [actions]);

  const handleSaveFieldChanges = useCallback(
    (upd: LogField) => {
      actions.saveFieldChanges(upd, editingField);
    },
    [actions, editingField],
  );

  /** ------------------ Gruppen & Suchfilter ------------------------ */
  const getFilteredLogGroups = useMemo((): FilteredLogGroup[] => {
    // Use template_data if available, otherwise fall back to gui
    const availableLogGroups = template_data?.available_log_groups || gui.availableLogGroups || [];
    const availableSensors = template_data?.available_sensors || gui.availableSensors || [];

    const all = [...availableLogGroups, ...availableSensors];
    const res: FilteredLogGroup[] = [];
    all.forEach((g) => {
      const gFields = logDatabase[g.name] || [];
      if (gFields.length) res.push({ ...g, fields: gFields });
    });
    return res;
  }, [template_data?.available_log_groups, template_data?.available_sensors, gui.availableLogGroups, gui.availableSensors, logDatabase]);

  /** ---------------- Drag-&-Drop-Callback -------------------------- */
  const handleDragEnd = useCallback(
    (result: any) => {
      const { source, destination } = result;
      if (!destination) return;

      /* Reorder innerhalb der Selected-Liste */
      if (
        source.droppableId === 'selectedFields' &&
        destination.droppableId === 'selectedFields'
      ) {
        actions.reorderFields(source.index, destination.index);
      }

      /* Hinzufügen von Available → Selected */
      if (
        source.droppableId === 'availableFields' &&
        destination.droppableId === 'selectedFields'
      ) {
        const [groupName, fieldIdx, logprefix] = result.draggableId.split('|');
        const grp = getFilteredLogGroups.find((g) => g.name === groupName);
        if (grp?.fields[+fieldIdx]) {
          handleAddField(grp.fields[+fieldIdx], groupName, logprefix, destination.index);
        }
      }
    },
    [getFilteredLogGroups, handleAddField, actions],
  );


  /** ------------------------------ Render -------------------------- */
  return (
    <FormControl fullWidth>
        {gui.label && (
          <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
            <FormLabel>
              {gui.label}
              {gui.tooltip && (
                <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                  <HelpOutlineIcon sx={{ ml: 1, fontSize: '1rem' }} />
                </Tooltip>
              )}
            </FormLabel>
          </Stack>
        )}

        <Paper variant="outlined" sx={{ p: 3, backgroundColor: 'background.default' }}>
          <Stack spacing={4}>
            {/* Label -------------------------------------------------- */}
            <Box>
              <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                Configuration Details
              </Typography>
              <TextField
                label="Configuration Label"
                value={label}
                onChange={handleLabelChange}
                fullWidth
                size="small"
                placeholder="Enter a descriptive name for this log configuration"
                sx={{ mt: 1 }}
              />
            </Box>

            {/* Trigger Settings -------------------------------------- */}
            <LogConfigTriggerSettings
              triggerSettings={triggerSettings}
              onChange={actions.updateTriggerSettings}
              availableSensors={template_data?.available_sensors || gui.availableSensors}
            />

            <Divider />

            {/* CSV Settings ------------------------------------------ */}
            <LogConfigCsvSettings
              csvSettings={csvSettings}
              onChange={handleCsvSettingsChange}
            />

            <Divider />

            {/* Field Selection Layout ----------------------------------- */}
            <LogConfigFieldSelector
              availableFields={getFilteredLogGroups}
              selectedFields={fields}
              onFieldAdd={handleAddField}
              onFieldRemove={handleRemoveField}
              onFieldEdit={handleEditField}
              onFieldReorder={handleDragEnd}
              isLoading={isLoading}
              error={error}
              searchTerm={searchTerm}
              expandedGroups={expandedGroups}
              onSearchChange={actions.updateSearchTerm}
              onToggleGroup={actions.toggleGroup}
            />

            {/* Settings-Editor Modal -------------------------------------- */}
            <LogConfigFieldEditorLazy
              field={editingField}
              isOpen={modalOpen}
              onSave={handleSaveFieldChanges}
              onCancel={handleCancelFieldEdit}
              logDatabase={logDatabase}
              availableLogGroups={template_data?.available_log_groups || gui.availableLogGroups}
              availableSensors={template_data?.available_sensors || gui.availableSensors}
            />
          </Stack>
        </Paper>
      </FormControl>
  );
}