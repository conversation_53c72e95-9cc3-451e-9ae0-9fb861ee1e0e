// components/inputs/logconfig/LogConfigFieldEditor.tsx
import React, { useCallback } from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Box,
  Typography,
  IconButton,
  Stack,
  Divider,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import {
  LogField,
  EditingLogField,
  LogDatabase,
  LogFieldSetting,
  LogGroup,
  SettingChangeHandler,
} from '../types/LogConfigTypes';
import LogSettingCols from '../LogSettingCols';
import LogSettingColsPS from '../LogSettingColsPS';
import LogSettingEccFRD from '../LogSettingEccFRD';
import LogSettingAsciiFormat from '../LogSettingAsciiFormat';

interface LogConfigFieldEditorProps {
  field: EditingLogField | null;
  isOpen: boolean;
  onSave: (field: LogField) => void;
  onCancel: () => void;
  logDatabase: LogDatabase;
  availableLogGroups?: LogGroup[];
  availableSensors?: LogGroup[];
}

const LogConfigFieldEditor = React.memo(function LogConfigFieldEditor({
  field,
  isOpen,
  onSave,
  onCancel,
  logDatabase,
  availableLogGroups = [],
  availableSensors = [],
}: LogConfigFieldEditorProps) {
  const [editingFieldState, setEditingFieldState] = React.useState<EditingLogField | null>(null);

  // Update local state when field prop changes
  React.useEffect(() => {
    setEditingFieldState(field);
  }, [field]);

  const renderSettingComponent = useCallback(
    (
      componentId: string,
      setting: LogFieldSetting,
      editingField: LogField,
      onChangeSetting: SettingChangeHandler,
    ) => {
      // Ensure we're working with a deep copy of the field's setting data
      // or fall back to the default data from the setting definition
      const getSettingValue = (fieldObj: LogField, settingName: string, defaultData: any) => {
        if (fieldObj[settingName] !== undefined) {
          // Create a deep copy to avoid reference issues
          return Array.isArray(fieldObj[settingName])
            ? [...fieldObj[settingName]]
            : fieldObj[settingName];
        }
        return Array.isArray(defaultData) ? [...defaultData] : defaultData;
      };

      // Log the received componentId and data for debugging
      console.log('Rendering setting component:', componentId, editingField, setting);

      switch (componentId) {
        case 'logsetting_cols':
          return (
            <LogSettingCols
              value={getSettingValue(editingField, setting.name, setting.data)}
              onChange={(v) => onChangeSetting(setting.name, v)}
              name={setting.name}
              gui={{
                label: 'Column Names',
                tooltip: 'Define the column names for this field in the CSV output',
              }}
            />
          );
        case 'logsetting_cols_ps':
          return (
            <LogSettingColsPS
              value={getSettingValue(editingField, setting.name, setting.data)}
              onChange={(v) => onChangeSetting(setting.name, v)}
              name={setting.name}
              gui={{
                label: 'Column Names with Prefix/Suffix',
                tooltip:
                  'Define prefix, suffix, and column names for this field in the CSV output',
              }}
            />
          );
        case 'logsetting_ecc_FRD':
          return (
            <LogSettingEccFRD
              value={getSettingValue(editingField, setting.name, setting.data)}
              onChange={(v) => onChangeSetting(setting.name, v)}
              name={setting.name}
              gui={{
                label: 'Excentricity/Lever Arm (Front, Right, Down)',
                tooltip: 'Define the excentricity/lever arm values in meters',
                unit: 'm',
                max_digits: 3,
              }}
            />
          );
        case 'logsetting_ascii_format':
          return (
            <LogSettingAsciiFormat
              value={getSettingValue(editingField, setting.name, setting.data)}
              onChange={(v) => onChangeSetting(setting.name, v)}
              name={setting.name}
              gui={{
                label: 'ASCII Format Strings',
                tooltip: 'Define the C-style format strings for this field',
              }}
            />
          );
        default:
          console.warn('Unknown setting component type:', componentId);
          return <Typography>Unknown setting type: {componentId}</Typography>;
      }
    },
    [],
  );

  const handleSave = useCallback(() => {
    if (!editingFieldState) return;
    
    // Make sure we're working with a clean copy of the editing field
    const fieldToSave = JSON.parse(JSON.stringify(editingFieldState));
    // Remove the editIndex property from the actual field data that will be saved
    const { editIndex, ...fieldDataToSave } = fieldToSave;
    onSave(fieldDataToSave);
  }, [editingFieldState, onSave]);

  const handleSettingChange = useCallback(
    (name: string, value: any) => {
      if (!editingFieldState) return;
      
      // Create a deep copy of the current editing field
      const updatedField = JSON.parse(JSON.stringify(editingFieldState));
      // Update the specific setting with a deep copy of the new value
      updatedField[name] = Array.isArray(value) ? JSON.parse(JSON.stringify(value)) : value;
      // Update local state
      setEditingFieldState(updatedField);
    },
    [editingFieldState],
  );

  if (!editingFieldState) return null;

  // First, check if we directly stored the group name when adding the field
  let grpName = editingFieldState.groupName || '';

  // If not found, try to find the group by matching logprefix
  if (!grpName || !logDatabase[grpName]) {
    grpName = [...availableLogGroups, ...availableSensors].find(
      (g) => g.logprefix === editingFieldState.logprefix,
    )?.name || '';
  }

  // If still no group name was found, search directly using the field properties
  if (!grpName || !logDatabase[grpName]) {
    // Search for the field in all available groups
    for (const [possibleGroupName, groupFields] of Object.entries(logDatabase)) {
      if (Array.isArray(groupFields) &&
          groupFields.some((f: any) => f.field === editingFieldState.field)) {
        grpName = possibleGroupName;
        console.log(`Found field ${editingFieldState.field} in group ${grpName}`);
        break;
      }
    }
  }

  console.log('Group name for field:', grpName);
  console.log('Log database keys:', Object.keys(logDatabase));

  const dbField = logDatabase[grpName]?.find(
    (f: any) => f.field === editingFieldState.field,
  );

  if (!dbField) {
    console.error('Could not find database field for editing:', editingFieldState, 'in group:', grpName);
    return null;
  }

  return (
    <Dialog
      open={isOpen}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Edit Settings: {editingFieldState.caption}
          </Typography>
          <IconButton edge="end" onClick={onCancel}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Typography variant="subtitle2" color="text.secondary">
          {editingFieldState.logprefix ? `${editingFieldState.logprefix}:` : ''}{editingFieldState.field}
        </Typography>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {dbField.settings.map((s: LogFieldSetting) => (
            <Box key={s.name}>
              {renderSettingComponent(s.gui.component_id, s, editingFieldState, handleSettingChange)}
            </Box>
          ))}
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
});

// Custom comparison function for better memoization
LogConfigFieldEditor.displayName = 'LogConfigFieldEditor';

export default LogConfigFieldEditor;