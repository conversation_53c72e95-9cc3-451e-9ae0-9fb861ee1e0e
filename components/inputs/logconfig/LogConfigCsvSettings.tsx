// components/inputs/logconfig/LogConfigCsvSettings.tsx
import React from 'react';
import {
  Box,
  Typography,
} from '@mui/material';
import {
  CsvSettings,
  CsvSettingsChangeHandler,
} from '../types/LogConfigTypes';
import LogSettingCsvSettings from '../LogSettingCsvSettings';

interface LogConfigCsvSettingsProps {
  csvSettings: CsvSettings;
  onChange: CsvSettingsChangeHandler;
}

const LogConfigCsvSettings = React.memo(function LogConfigCsvSettings({
  csvSettings,
  onChange,
}: LogConfigCsvSettingsProps) {
  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
        CSV Output Settings
      </Typography>
      <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
        <LogSettingCsvSettings
          value={csvSettings}
          onChange={onChange}
          name="csv_settings"
          gui={{
            label: "CSV Output Settings",
            tooltip: "Configure how the CSV output files are formatted"
          }}
        />
      </Box>
    </Box>
  );
});

// Custom comparison function for better memoization
LogConfigCsvSettings.displayName = 'LogConfigCsvSettings';

export default LogConfigCsvSettings;