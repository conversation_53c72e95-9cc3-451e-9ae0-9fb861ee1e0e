// components/inputs/logconfig/LogConfigFieldEditorSkeleton.tsx
import React from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Box,
  Typography,
  IconButton,
  Stack,
  Divider,
  Skeleton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface LogConfigFieldEditorSkeletonProps {
  isOpen: boolean;
  onCancel: () => void;
}

export default function LogConfigFieldEditorSkeleton({
  isOpen,
  onCancel,
}: LogConfigFieldEditorSkeletonProps) {
  return (
    <Dialog
      open={isOpen}
      onClose={onCancel}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            <Skeleton width={200} />
          </Typography>
          <IconButton edge="end" onClick={onCancel}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Typography variant="subtitle2" color="text.secondary">
          <Skeleton width={150} />
        </Typography>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Skeleton for field settings */}
          {[1, 2, 3].map((i) => (
            <Box key={i}>
              <Skeleton variant="text" width={120} height={24} sx={{ mb: 1 }} />
              <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 1 }} />
            </Box>
          ))}
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onCancel}>
          Cancel
        </Button>
        <Button variant="contained" disabled>
          <Skeleton width={80} />
        </Button>
      </DialogActions>
    </Dialog>
  );
}