// useLogConfigReducer.ts - State management with useReducer for LogConfigInput
import { useReducer, useCallback } from 'react';
import {
  LogField,
  TriggerSettings,
  CsvSettings,
  EditingLogField,
  LogDatabase,
  DatabaseLogField,
} from '../types/LogConfigTypes';

/**
 * Complete state interface for LogConfigInput component
 */
export interface LogConfigState {
  label: string;
  triggerSettings: TriggerSettings;
  fields: LogField[];
  csvSettings: CsvSettings;
  searchTerm: string;
  expandedGroups: string[];
  editingField: EditingLogField | null;
  modalOpen: boolean;
  logDatabase: LogDatabase;
}

/**
 * Union type for all possible actions
 */
export type LogConfigAction =
  | { type: 'UPDATE_LABEL'; payload: string }
  | { type: 'UPDATE_TRIGGER_SETTINGS'; payload: TriggerSettings }
  | { type: 'UPDATE_FIELDS'; payload: LogField[] }
  | { type: 'ADD_FIELD'; payload: { field: DatabaseLogField; groupName: string; logprefix: string; index?: number } }
  | { type: 'REMOVE_FIELD'; payload: number }
  | { type: 'REORDER_FIELDS'; payload: { sourceIndex: number; destinationIndex: number } }
  | { type: 'UPDATE_CSV_SETTINGS'; payload: CsvSettings }
  | { type: 'UPDATE_SEARCH_TERM'; payload: string }
  | { type: 'TOGGLE_GROUP'; payload: string }
  | { type: 'SET_EDITING_FIELD'; payload: EditingLogField | null }
  | { type: 'SET_MODAL_OPEN'; payload: boolean }
  | { type: 'SET_LOG_DATABASE'; payload: LogDatabase }
  | { type: 'RESET_STATE'; payload: Partial<LogConfigState> };

/**
 * Initial state factory function
 */
export const createInitialState = (
  label = '',
  triggerSettings: TriggerSettings = { trigger: 'RTS_TIME', dt: 0.1 },
  fields: LogField[] = [],
  csvSettings: CsvSettings = {
    delim: ", ",
    delim_sub: ",",
    eol: "\n",
    delimiter_after_last_col: false,
    output_full_header: true,
    output_header_row: true,
    header_prefix: "# ",
  }
): LogConfigState => ({
  label,
  triggerSettings,
  fields,
  csvSettings,
  searchTerm: '',
  expandedGroups: [],
  editingField: null,
  modalOpen: false,
  logDatabase: {},
});

/**
 * Reducer function for LogConfig state management
 */
export function logConfigReducer(state: LogConfigState, action: LogConfigAction): LogConfigState {
  switch (action.type) {
    case 'UPDATE_LABEL':
      return {
        ...state,
        label: action.payload,
      };

    case 'UPDATE_TRIGGER_SETTINGS':
      return {
        ...state,
        triggerSettings: action.payload,
      };

    case 'UPDATE_FIELDS':
      return {
        ...state,
        fields: action.payload,
      };

    case 'ADD_FIELD': {
      const { field, groupName, logprefix, index } = action.payload;
      const newField: LogField = {
        field: field.field,
        logprefix,
        caption: field.caption,
        precision: field.precision,
        groupName,
      };
      
      // Copy field settings
      field.settings.forEach((s) => {
        newField[s.name] = [...s.data];
      });

      const newFields = [...state.fields];
      if (typeof index === 'number' && index >= 0 && index <= newFields.length) {
        newFields.splice(index, 0, newField);
      } else {
        newFields.push(newField);
      }

      return {
        ...state,
        fields: newFields,
      };
    }

    case 'REMOVE_FIELD': {
      const newFields = [...state.fields];
      newFields.splice(action.payload, 1);
      return {
        ...state,
        fields: newFields,
      };
    }

    case 'REORDER_FIELDS': {
      const { sourceIndex, destinationIndex } = action.payload;
      const newFields = [...state.fields];
      const [movedField] = newFields.splice(sourceIndex, 1);
      newFields.splice(destinationIndex, 0, movedField);
      return {
        ...state,
        fields: newFields,
      };
    }

    case 'UPDATE_CSV_SETTINGS':
      // Only update if settings actually changed to prevent unnecessary re-renders
      if (JSON.stringify(state.csvSettings) === JSON.stringify(action.payload)) {
        return state;
      }
      return {
        ...state,
        csvSettings: action.payload,
      };

    case 'UPDATE_SEARCH_TERM':
      return {
        ...state,
        searchTerm: action.payload,
      };

    case 'TOGGLE_GROUP': {
      const groupName = action.payload;
      const expandedGroups = state.expandedGroups.includes(groupName)
        ? state.expandedGroups.filter(g => g !== groupName)
        : [...state.expandedGroups, groupName];
      
      return {
        ...state,
        expandedGroups,
      };
    }

    case 'SET_EDITING_FIELD':
      return {
        ...state,
        editingField: action.payload,
      };

    case 'SET_MODAL_OPEN':
      return {
        ...state,
        modalOpen: action.payload,
        // Clear editing field when modal closes
        ...(action.payload === false && { editingField: null }),
      };

    case 'SET_LOG_DATABASE':
      return {
        ...state,
        logDatabase: action.payload,
      };

    case 'RESET_STATE':
      return {
        ...state,
        ...action.payload,
      };

    default:
      return state;
  }
}

/**
 * Custom hook that provides the reducer and action creators
 */
export function useLogConfigReducer(
  initialLabel = '',
  initialTriggerSettings: TriggerSettings = { trigger: 'RTS_TIME', dt: 0.1 },
  initialFields: LogField[] = [],
  initialCsvSettings: CsvSettings = {
    delim: ", ",
    delim_sub: ",",
    eol: "\n",
    delimiter_after_last_col: false,
    output_full_header: true,
    output_header_row: true,
    header_prefix: "# ",
  }
) {
  const [state, dispatch] = useReducer(
    logConfigReducer,
    createInitialState(initialLabel, initialTriggerSettings, initialFields, initialCsvSettings)
  );

  // Action creators for better ergonomics
  const actions = {
    updateLabel: useCallback((label: string) => {
      dispatch({ type: 'UPDATE_LABEL', payload: label });
    }, []),

    updateTriggerSettings: useCallback((settings: TriggerSettings) => {
      dispatch({ type: 'UPDATE_TRIGGER_SETTINGS', payload: settings });
    }, []),

    updateFields: useCallback((fields: LogField[]) => {
      dispatch({ type: 'UPDATE_FIELDS', payload: fields });
    }, []),

    addField: useCallback((field: DatabaseLogField, groupName: string, logprefix: string, index?: number) => {
      dispatch({ type: 'ADD_FIELD', payload: { field, groupName, logprefix, index } });
    }, []),

    removeField: useCallback((index: number) => {
      dispatch({ type: 'REMOVE_FIELD', payload: index });
    }, []),

    reorderFields: useCallback((sourceIndex: number, destinationIndex: number) => {
      dispatch({ type: 'REORDER_FIELDS', payload: { sourceIndex, destinationIndex } });
    }, []),

    updateCsvSettings: useCallback((settings: CsvSettings) => {
      dispatch({ type: 'UPDATE_CSV_SETTINGS', payload: settings });
    }, []),

    updateSearchTerm: useCallback((term: string) => {
      dispatch({ type: 'UPDATE_SEARCH_TERM', payload: term });
    }, []),

    toggleGroup: useCallback((groupName: string) => {
      dispatch({ type: 'TOGGLE_GROUP', payload: groupName });
    }, []),

    setEditingField: useCallback((field: EditingLogField | null) => {
      dispatch({ type: 'SET_EDITING_FIELD', payload: field });
    }, []),

    setModalOpen: useCallback((open: boolean) => {
      dispatch({ type: 'SET_MODAL_OPEN', payload: open });
    }, []),

    setLogDatabase: useCallback((database: LogDatabase) => {
      dispatch({ type: 'SET_LOG_DATABASE', payload: database });
    }, []),

    resetState: useCallback((newState: Partial<LogConfigState>) => {
      dispatch({ type: 'RESET_STATE', payload: newState });
    }, []),

    // Convenience method for editing fields
    editField: useCallback((field: LogField, index: number) => {
      const editingField: EditingLogField = { ...field, editIndex: index };
      dispatch({ type: 'SET_EDITING_FIELD', payload: editingField });
      dispatch({ type: 'SET_MODAL_OPEN', payload: true });
    }, []),

    // Convenience method for saving field changes
    saveFieldChanges: useCallback((updatedField: LogField, editingField: EditingLogField | null) => {
      if (editingField && editingField.editIndex !== undefined) {
        const editIndex = editingField.editIndex;
        dispatch({ 
          type: 'UPDATE_FIELDS', 
          payload: state.fields.map((field, index) => 
            index === editIndex ? JSON.parse(JSON.stringify(updatedField)) : field
          )
        });
      }
      dispatch({ type: 'SET_MODAL_OPEN', payload: false });
    }, [state.fields]),

    // Convenience method for canceling field edit
    cancelFieldEdit: useCallback(() => {
      dispatch({ type: 'SET_MODAL_OPEN', payload: false });
      // Clear editing field after modal transition
      setTimeout(() => {
        dispatch({ type: 'SET_EDITING_FIELD', payload: null });
      }, 200);
    }, []),
  };

  return {
    state,
    dispatch,
    actions,
  };
}