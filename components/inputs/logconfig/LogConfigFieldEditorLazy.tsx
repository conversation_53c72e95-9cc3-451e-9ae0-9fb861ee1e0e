// components/inputs/logconfig/LogConfigFieldEditorLazy.tsx
import React, { Suspense, lazy } from 'react';
import {
  LogField,
  EditingLogField,
  LogDatabase,
  LogGroup,
} from '../types/LogConfigTypes';
import LogConfigFieldEditorSkeleton from './LogConfigFieldEditorSkeleton';

// Lazy load the actual Field Editor component
const LogConfigFieldEditor = lazy(() => import('./LogConfigFieldEditor'));

interface LogConfigFieldEditorLazyProps {
  field: EditingLogField | null;
  isOpen: boolean;
  onSave: (field: LogField) => void;
  onCancel: () => void;
  logDatabase: LogDatabase;
  availableLogGroups?: LogGroup[];
  availableSensors?: LogGroup[];
}

export default function LogConfigFieldEditorLazy(props: LogConfigFieldEditorLazyProps) {
  // Only render the actual component when the modal is open
  if (!props.isOpen) {
    return null;
  }

  return (
    <Suspense fallback={<LogConfigFieldEditorSkeleton isOpen={props.isOpen} onCancel={props.onCancel} />}>
      <LogConfigFieldEditor {...props} />
    </Suspense>
  );
}