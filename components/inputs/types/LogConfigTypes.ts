// LogConfigTypes.ts - TypeScript interfaces for LogConfigInput component

/**
 * Represents a log field with its metadata and settings
 */
export interface LogField {
  field: string;
  logprefix: string;
  caption: string;
  precision: number;
  groupName: string;
  // Dynamic settings based on field configuration
  [settingName: string]: any;
}

/**
 * Represents a log field setting configuration
 */
export interface LogFieldSetting {
  name: string;
  data: any; // Default data for the setting - can be arrays, primitives, or objects
  gui: {
    component_id: string;
    label?: string;
    tooltip?: string;
    unit?: string;
    max_digits?: number;
  };
}

/**
 * Represents a database log field with its settings
 */
export interface DatabaseLogField {
  field: string;
  caption: string;
  precision: number;
  settings: LogFieldSetting[];
}

/**
 * Represents a log group (sensor or log group)
 */
export interface LogGroup {
  name: string;
  label: string;
  logprefix: string;
}

/**
 * Represents trigger settings for different trigger types
 */
export interface TriggerSettings {
  trigger: 'RTS_TIME' | 'RTS_MEAS' | 'RTS_PREDICTOR';
  dt?: number; // For RTS_TIME
  sensor?: string; // For RTS_MEAS
  step?: number; // For RTS_MEAS and RTS_PREDICTOR
}

/**
 * Represents CSV output settings
 */
export interface CsvSettings {
  delim?: string;
  delim_sub?: string;
  eol?: string;
  delimiter_after_last_col?: boolean;
  output_full_header?: boolean;
  output_header_row?: boolean;
  header_prefix?: string;
}

/**
 * Represents the complete log configuration value
 */
export interface LogConfigValue {
  label?: string;
  trigger?: 'RTS_TIME' | 'RTS_MEAS' | 'RTS_PREDICTOR';
  dt?: number;
  sensor?: string;
  step?: number;
  fields?: LogField[];
  csv_settings?: CsvSettings;
}

/**
 * Represents the GUI configuration for the LogConfigInput component
 */
export interface LogConfigGui {
  label?: string;
  tooltip?: string;
  availableLogGroups?: LogGroup[];
  availableSensors?: LogGroup[];
  [key: string]: any; // For additional GUI properties
}

/**
 * Represents template data available to the component
 */
export interface LogConfigTemplateData {
  available_log_groups?: LogGroup[];
  available_sensors?: LogGroup[];
  [key: string]: any; // For additional template data
}

/**
 * Props interface for the LogConfigInput component
 */
export interface LogConfigInputProps {
  value: LogConfigValue;
  onChange: (value: LogConfigValue) => void;
  name: string;
  gui: LogConfigGui;
  template_data?: LogConfigTemplateData;
}

/**
 * Represents a log field being edited with additional metadata
 */
export interface EditingLogField extends LogField {
  editIndex?: number; // Index of the field being edited
}

/**
 * Represents the log database structure
 */
export interface LogDatabase {
  [groupName: string]: DatabaseLogField[];
}

/**
 * Represents a filtered log group with its fields
 */
export interface FilteredLogGroup extends LogGroup {
  fields: DatabaseLogField[];
}

/**
 * Event handler types for better type safety
 */
export type LogFieldChangeHandler = (field: LogField, groupName: string, logprefix: string, index?: number) => void;
export type LogFieldEditHandler = (field: LogField, index: number) => void;
export type LogFieldSaveHandler = (updatedField: LogField) => void;
export type LogFieldRemoveHandler = (index: number) => void;
export type CsvSettingsChangeHandler = (settings: CsvSettings) => void;
export type TriggerChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;
export type SettingChangeHandler = (name: string, value: any) => void;