import React, { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Card,
  CardContent,
  Paper,
  Divider,
  useTheme,
  Stack
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Tooltip from '@mui/material/Tooltip';
import InfoIcon from '@mui/icons-material/Info';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface IMUAxisOrientationProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  tooltip?: string;
}

const StyledCard = styled(Card)(({ theme }) => ({
  width: '100%',
  marginBottom: theme.spacing(2),
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[50],
}));

const InfoBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'start',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));

const CoordinateBox = styled(Box)(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
}));

type Direction = 'F' | 'B' | 'L' | 'R' | 'U' | 'D';

const isValidDirection = (dir: string): dir is Direction => {
  return ['F', 'B', 'L', 'R', 'U', 'D'].includes(dir);
};

const directionLabels: Record<Direction, string> = {
  F: 'Front',
  B: 'Back',
  L: 'Left',
  R: 'Right',
  U: 'Up',
  D: 'Down',
};

// Define vectors for each direction in vehicle coordinates
const directionVectors: Record<Direction, number[]> = {
  F: [0, 1, 0],   // Front points along +Y
  B: [0, -1, 0],  // Back points along -Y
  L: [-1, 0, 0],  // Left points along -X
  R: [1, 0, 0],   // Right points along +X
  U: [0, 0, 1],   // Up points along +Z
  D: [0, 0, -1],  // Down points along -Z
};

// Get available options for Y-axis based on X-axis selection
const getAvailableYAxisOptions = (xAxisValue: Direction): Direction[] => {
  const xVec = directionVectors[xAxisValue];
  const availableOptions: Direction[] = [];

  // Check each possible direction for Y-axis
  Object.entries(directionVectors).forEach(([dir, yVec]) => {
    if (isValidDirection(dir)) {
      // Skip the direction parallel or anti-parallel to X-axis
      if (dir === xAxisValue || isOppositeDirection(xVec, yVec)) {
        return;
      }
      availableOptions.push(dir);
    }
  });

  return availableOptions;
};

// Check if two vectors are opposite (parallel but pointing in opposite directions)
const isOppositeDirection = (vec1: number[], vec2: number[]): boolean => {
  return vec1.every((component, i) => component === -vec2[i]) ||
         vec1.every((component, i) => component === vec2[i]);
};

// Calculate Z-axis based on right-handed coordinate system using cross product
const calculateZAxis = (x: Direction, y: Direction): Direction => {
  const xVec = directionVectors[x];
  const yVec = directionVectors[y];

  // Calculate cross product (z = x × y)
  const zVec = [
    xVec[1] * yVec[2] - xVec[2] * yVec[1],
    xVec[2] * yVec[0] - xVec[0] * yVec[2],
    xVec[0] * yVec[1] - xVec[1] * yVec[0],
  ];

  // Normalize the result to get a unit vector
  const magnitude = Math.sqrt(zVec[0] * zVec[0] + zVec[1] * zVec[1] + zVec[2] * zVec[2]);
  const normalizedZVec = zVec.map(component => component / magnitude);

  // Compare with all possible direction vectors to find the best match
  let bestMatch: Direction = 'U';
  let bestDotProduct = -Infinity;

  Object.entries(directionVectors).forEach(([dir, dirVec]) => {
    if (isValidDirection(dir)) {
      const dotProduct = normalizedZVec[0] * dirVec[0] +
                        normalizedZVec[1] * dirVec[1] +
                        normalizedZVec[2] * dirVec[2];
      if (dotProduct > bestDotProduct) {
        bestDotProduct = dotProduct;
        bestMatch = dir;
      }
    }
  });

  return bestMatch;
};

const IMUAxisOrientation: React.FC<IMUAxisOrientationProps> = ({
  value,
  onChange,
  label = 'IMU Axis Orientation',
  tooltip,
}) => {
  const theme = useTheme();
  
  // Initialize state from props.value, or use defaults if value is empty
  type Axes = {
    x: Direction;
    y: Direction;
    z: Direction;
  };

  const [axes, setAxes] = useState<Axes>(() => {
    if (value && value.length === 3 && isValidDirection(value[0]) && isValidDirection(value[1]) && isValidDirection(value[2])) {
      return {
        x: value[0] as Direction,
        y: value[1] as Direction,
        z: value[2] as Direction,
      };
    }
    const defaultX: Direction = 'F';
    const defaultY: Direction = 'L';
    return {
      x: defaultX,
      y: defaultY,
      z: calculateZAxis(defaultX, defaultY) as Direction,
    };
  });

  // Update internal state when prop value changes
  useEffect(() => {
    if (value && value.length === 3 &&
        isValidDirection(value[0]) &&
        isValidDirection(value[1]) &&
        isValidDirection(value[2]) &&
        (value[0] !== axes.x || value[1] !== axes.y || value[2] !== axes.z)) {
      setAxes({
        x: value[0],
        y: value[1],
        z: value[2],
      } as Axes);
    }
  }, [value]);

  const handleAxisChange = (axis: 'x' | 'y', newValue: string) => {
    if (!isValidDirection(newValue)) return;
    
    const newAxes = { ...axes };

    if (axis === 'x') {
      newAxes.x = newValue;
      // If current Y-axis is not valid with new X-axis, reset it to first valid option
      const validYOptions = getAvailableYAxisOptions(newValue);
      const firstValidY = validYOptions[0];
      if (!validYOptions.includes(newAxes.y) && isValidDirection(firstValidY)) {
        newAxes.y = firstValidY;
      }
    } else {
      newAxes.y = newValue;
    }

    const calculatedZ = calculateZAxis(newAxes.x, newAxes.y);
    if (isValidDirection(calculatedZ)) {
      newAxes.z = calculatedZ;
      setAxes(newAxes);
      onChange(`${newAxes.x}${newAxes.y}${newAxes.z}`);
    }
  };

  const formControl = (
    <StyledCard elevation={0}>
      <CardContent sx={{ p: 0 }}>
        <Box sx={{ mb: 3 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="subtitle1" color="text.primary" sx={{ fontWeight: 500 }}>
              {label}
              {tooltip && (
                <Tooltip title={<div dangerouslySetInnerHTML={{ __html: tooltip }} />}>
                  <HelpOutlineIcon sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }} />
                </Tooltip>
              )}
            </Typography>
          </Stack>
          
          <InfoBox>
            <InfoIcon color="primary" sx={{ mt: 0.5 }} />
            <Typography variant="body2" color="text.secondary">
              Define how your IMU is mounted on the vehicle by specifying the direction of its X and Y axes. 
              The Z-axis will be automatically determined to maintain a right-handed coordinate system. 
              Think of your right hand: thumb (X), index finger (Y), and middle finger (Z) 
              to visualize the orientation.
            </Typography>
          </InfoBox>
        </Box>

        <StyledPaper elevation={0}>
          <Typography variant="subtitle2" gutterBottom color="text.primary" sx={{ fontWeight: 500 }}>
            Vehicle Reference Frame
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ p: 1, border: `1px solid ${theme.palette.divider}`, borderRadius: 1 }}>
                <Typography variant="body2" color="primary" gutterBottom>
                  Front/Back
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Vehicle's driving direction
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ p: 1, border: `1px solid ${theme.palette.divider}`, borderRadius: 1 }}>
                <Typography variant="body2" color="primary" gutterBottom>
                  Left/Right
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Vehicle's sides
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ p: 1, border: `1px solid ${theme.palette.divider}`, borderRadius: 1 }}>
                <Typography variant="body2" color="primary" gutterBottom>
                  Up/Down
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Vertical axis
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </StyledPaper>

        <Stack spacing={3}>
          <CoordinateBox>
            <Typography variant="subtitle2" gutterBottom color="text.primary" sx={{ fontWeight: 500 }}>
              X-Axis Direction
            </Typography>
            <Typography variant="caption" display="block" gutterBottom color="text.secondary">
              Select where the IMU's X-axis points
            </Typography>
            <Select
              value={axes.x}
              onChange={(e) => handleAxisChange('x', e.target.value)}
              size="small"
              fullWidth
            >
              {Object.keys(directionLabels).map((dir) => (
                <MenuItem key={dir} value={dir}>
                  {directionLabels[dir as keyof typeof directionLabels]}
                </MenuItem>
              ))}
            </Select>
          </CoordinateBox>

          <CoordinateBox>
            <Typography variant="subtitle2" gutterBottom color="text.primary" sx={{ fontWeight: 500 }}>
              Y-Axis Direction
            </Typography>
            <Typography variant="caption" display="block" gutterBottom color="text.secondary">
              Select where the IMU's Y-axis points
            </Typography>
            <Select
              value={axes.y}
              onChange={(e) => handleAxisChange('y', e.target.value)}
              size="small"
              fullWidth
            >
              {getAvailableYAxisOptions(axes.x).map((dir) => (
                <MenuItem key={dir} value={dir}>
                  {directionLabels[dir as keyof typeof directionLabels]}
                </MenuItem>
              ))}
            </Select>
          </CoordinateBox>

          <CoordinateBox>
            <Typography variant="subtitle2" gutterBottom color="text.primary" sx={{ fontWeight: 500 }}>
              Z-Axis Direction
            </Typography>
            <Typography variant="caption" display="block" gutterBottom color="text.secondary">
              Automatically determined based on right-hand rule
            </Typography>
            <Select
              value={axes.z}
              size="small"
              fullWidth
              disabled
            >
              <MenuItem value={axes.z}>
                {directionLabels[axes.z]}
              </MenuItem>
            </Select>
          </CoordinateBox>
        </Stack>
      </CardContent>
    </StyledCard>
  );

  return formControl;
};

export default IMUAxisOrientation;