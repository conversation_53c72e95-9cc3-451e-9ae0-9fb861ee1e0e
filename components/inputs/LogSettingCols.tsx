// components/inputs/LogSettingCols.tsx
import { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogSettingColsProps {
  value: string[];
  onChange: (value: string[]) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    max_chars?: number;
    [key: string]: any;
  };
}

export default function LogSettingCols({
  value = [],
  onChange,
  name,
  gui,
}: LogSettingColsProps) {
  const [localValues, setLocalValues] = useState<string[]>(value);

  useEffect(() => {
    setLocalValues(value);
  }, [value]);

  const handleChange = (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValues = [...localValues];
    newValues[index] = e.target.value;
    setLocalValues(newValues);
    // Update parent component immediately to ensure changes are tracked
    onChange(newValues);
  };

  // Handle blur event to update parent only when focus leaves the field
  const handleBlur = () => {
    onChange(localValues);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <Stack spacing={2}>
        {localValues.map((val, index) => (
          <Box key={index} display="flex" alignItems="center">
            <TextField
              fullWidth
              size="small"
              value={val}
              onChange={handleChange(index)}
              onBlur={handleBlur}
              placeholder={`Column ${index + 1}`}
              inputProps={{
                maxLength: gui.max_chars,
              }}
            />
          </Box>
        ))}

      </Stack>
    </FormControl>
  );
}
