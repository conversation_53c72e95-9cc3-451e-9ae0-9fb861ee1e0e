import { FormControl, FormControlLabel, Checkbox as MUICheckbox, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Deep equality comparison for any data type
function isEqual(a: any, b: any): boolean {
  // Handle null/undefined
  if (a === b) return true;
  if (a === null || b === null) return false;
  if (a === undefined || b === undefined) return false;

  // Handle different types
  if (typeof a !== typeof b) return false;

  // Handle arrays
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => isEqual(item, b[index]));
  }

  // Handle objects
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => 
      Object.prototype.hasOwnProperty.call(b, key) && 
      isEqual(a[key], b[key])
    );
  }

  // Handle primitives
  return Object.is(a, b);
}

interface CheckboxGUIProps {
  value: any;
  onChange: (value: boolean) => void;
  name: string;
  gui: {
    label?: string;
    value_checked?: any;
    value_unchecked?: any;
    tooltip?: string;
    [key: string]: any;
  };
}

export default function Checkbox({
  value,
  onChange,
  gui,
}: CheckboxGUIProps) {
  // Use deep equality comparison when value_checked is defined
  const checked = gui.value_checked !== undefined 
    ? isEqual(value, gui.value_checked) 
    : Boolean(value);

  return (
    <FormControl>
      <FormControlLabel
        control={
          <MUICheckbox
            checked={checked}
            onChange={(e) => onChange(e.target.checked)}
          />
        }
        label={
          <Stack direction="row" spacing={1} alignItems="center">
            {gui.label || ''}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </Stack>
        }
      />
    </FormControl>
  );
}