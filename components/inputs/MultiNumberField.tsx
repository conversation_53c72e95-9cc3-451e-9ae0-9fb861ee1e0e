// components/inputs/MultiNumberField.tsx
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
} from '@mui/material';
import { useTemplateStore } from '../../lib/stores/templateStore';
import { useState } from 'react';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { validateDisplayNumber } from '../../utils/numberValidation';

interface MultiNumberFieldGUIProps {
  value: number[];
  onChange: (value: number[]) => void;
  name: string;
  gui: {
    label?: string;
    min_value?: number;
    max_value?: number;
    max_digits?: number;
    integer?: boolean;
    tooltip?: string;
    unit?: string;
    axes?: string[];
    visible_axes?: number[];
    multiply?: number;
    [key: string]: any;
  };
}

export default function MultiNumberField({
  value,
  onChange,
  name,
  gui,
}: MultiNumberFieldGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const [touched, setTouched] = useState<boolean[]>(
    new Array(value.length).fill(false)
  );
  const error = getValidationError(name);
  const multiplyFactor = gui.multiply || 1;

  const defaultAxes = ['X', 'Y', 'Z'];
  const axisLabels = gui.axes || defaultAxes;
  const visibleAxes =
    gui.visible_axes || Array.from({ length: value.length }, (_, i) => i);

  const handleChange =
    (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      const parsedValue = newValue === '' ? NaN : Number(newValue) / multiplyFactor;
      const newValues = [...value];
      newValues[index] = parsedValue;
      onChange(newValues);
    };

  const handleBlur =
    (index: number) => (e: React.FocusEvent<HTMLInputElement>) => {
      const newTouched = [...touched];
      newTouched[index] = true;
      setTouched(newTouched);

      const newValue = e.target.value;
      if (newValue === '') {
        setValidationError(`${name}_${index}`, 'Required');
        return;
      }
      const num = Number(newValue) / multiplyFactor;
      if (isNaN(num)) {
        setValidationError(`${name}_${index}`, 'Must be a number');
        return;
      }
      const validationError = validateDisplayNumber(num, gui);
      setValidationError(`${name}_${index}`, validationError);
    };

  const constraints = [];
  if (gui.min_value !== undefined) constraints.push(`min: ${gui.min_value}`);
  if (gui.max_value !== undefined) constraints.push(`max: ${gui.max_value}`);
  if (!gui.integer && gui.max_digits !== undefined && gui.max_digits > 0) {
    constraints.push(`precision: ${gui.max_digits} decimals`);
  }
  if (gui.integer) constraints.push('integers only');
  const constraintText =
    constraints.length > 0 ? constraints.join(', ') : undefined;

  return (
    <FormControl fullWidth error={!!error}>
      <Stack direction="row" spacing={1} alignItems="center">
        {gui.label && (
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        )}
      </Stack>
      <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
        {value.map((val, index) => {
          if (!visibleAxes.includes(index)) return null;
          const fieldError = getValidationError(`${name}_${index}`);
          const helperText = touched[index] ? fieldError || constraintText : '';
          const displayValue = isNaN(val) ? '' : val * multiplyFactor;
          const displayStep = gui.integer
            ? (1 * multiplyFactor).toString()
            : gui.max_digits
              ? (Math.pow(10, -gui.max_digits) * multiplyFactor).toString()
              : 'any';

          return (
            <TextField
              key={index}
              size="small"
              type="number"
              label={`${axisLabels[index]}${gui.unit ? ` (${gui.unit})` : ''}`}
              value={displayValue}
              onChange={handleChange(index)}
              onBlur={handleBlur(index)}
              error={!!fieldError && touched[index]}
              helperText={helperText}
              inputProps={{
                step: displayStep,
                min: gui.min_value,
                max: gui.max_value,
              }}
            />
          );
        })}
      </Stack>
    </FormControl>
  );
}
