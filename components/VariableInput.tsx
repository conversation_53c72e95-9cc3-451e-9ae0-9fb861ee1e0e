import React from 'react';
import { Box, TextField, IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

interface Variable {
  name: string;
  value: string;
}

interface VariableInputProps {
  variable: Variable;
  onChange: (variable: Variable) => void;
  onDelete: () => void;
}

export function VariableInput({ variable, onChange, onDelete }: VariableInputProps) {
  const handleValueChange = (value: string) => {
    try {
      // Validate JSON format
      JSON.parse(value);
      onChange({ ...variable, value });
    } catch (e) {
      // Let the user continue typing even if JSO<PERSON> is invalid
      onChange({ ...variable, value });
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
      <TextField
        label="Variable Name"
        value={variable.name}
        onChange={(e) => onChange({ ...variable, name: e.target.value })}
        size="small"
      />
      <TextField
        label="Value (JSON)"
        value={variable.value}
        onChange={(e) => handleValueChange(e.target.value)}
        error={!isValidJson(variable.value)}
        helperText={!isValidJson(variable.value) ? "Invalid JSON format" : ""}
        size="small"
        fullWidth
      />
      <IconButton onClick={onDelete} color="error">
        <DeleteIcon />
      </IconButton>
    </Box>
  );
}

function isValidJson(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
