"use client"

import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  Divider,
  Button,
  Paper
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { TicketNotification } from '@/lib/hooks/useNotifications';
import NotificationItem from './NotificationItem';

interface NotificationDropdownProps {
  notifications: TicketNotification[];
  onNotificationClick?: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  onNotificationClick
}) => {
  const router = useRouter();

  const handleNotificationClick = (ticketId: string) => {
    // Navigate to tickets page with the specific ticket ID
    router.push(`/tickets?ticketId=${ticketId}`);
    onNotificationClick?.();
  };

  const handleViewAllClick = () => {
    router.push('/tickets');
    onNotificationClick?.();
  };

  // Limit to last 10 notifications for the dropdown
  const displayNotifications = notifications.slice(0, 10);

  return (
    <Paper elevation={0} sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Notifications
        </Typography>
        {notifications.length > 0 && (
          <Typography variant="body2" color="text.secondary">
            {notifications.length} recent update{notifications.length !== 1 ? 's' : ''}
          </Typography>
        )}
      </Box>

      {/* Notifications List */}
      <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
        {displayNotifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No recent notifications
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              You'll see updates here when your support tickets are resolved or need your attention.
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {displayNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem sx={{ p: 0 }}>
                  <NotificationItem
                    notification={notification}
                    onClick={() => handleNotificationClick(notification.id)}
                  />
                </ListItem>
                {index < displayNotifications.length - 1 && (
                  <Divider variant="inset" component="li" />
                )}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Footer */}
      {notifications.length > 0 && (
        <>
          <Divider />
          <Box sx={{ p: 2 }}>
            <Button
              fullWidth
              variant="text"
              onClick={handleViewAllClick}
              sx={{ 
                textTransform: 'none',
                fontWeight: 500
              }}
            >
              View All Tickets
            </Button>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default NotificationDropdown;
