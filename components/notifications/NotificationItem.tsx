"use client"

import React from 'react';
import {
  Box,
  Typography,
  Chip,
  ButtonBase,
  Stack
} from '@mui/material';
import { formatDistanceToNow } from 'date-fns';
import { TicketNotification } from '@/lib/hooks/useNotifications';

interface NotificationItemProps {
  notification: TicketNotification;
  onClick: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onClick
}) => {
  // Status display configuration
  const statusConfig = {
    resolved: {
      label: 'Resolved',
      color: 'success' as const,
      message: 'Your ticket has been resolved'
    },
    waiting_on_customer: {
      label: 'Waiting for You',
      color: 'info' as const,
      message: 'Your response is needed'
    }
  };

  const config = statusConfig[notification.status];
  
  // Format the relative time
  const timeAgo = formatDistanceToNow(new Date(notification.updated_at), { 
    addSuffix: true 
  });

  // Get target information for context
  const getTargetInfo = () => {
    if (!notification.ticket_targets || notification.ticket_targets.length === 0) {
      return null;
    }
    
    const target = notification.ticket_targets[0];
    return `${target.target_type} #${target.target_id}`;
  };

  const targetInfo = getTargetInfo();

  return (
    <ButtonBase
      onClick={onClick}
      sx={{
        width: '100%',
        p: 2,
        display: 'block',
        textAlign: 'left',
        borderRadius: 0,
        '&:hover': {
          backgroundColor: 'action.hover'
        },
        '&:focus': {
          backgroundColor: 'action.focus'
        }
      }}
    >
      <Box sx={{ width: '100%' }}>
        {/* Header with title and status */}
        <Stack direction="row" spacing={1} alignItems="flex-start" sx={{ mb: 1 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              lineHeight: 1.4
            }}
          >
            #{notification.id} {notification.title}
          </Typography>
          <Chip
            label={config.label}
            color={config.color}
            size="small"
            sx={{
              fontSize: '0.7rem',
              height: 20,
              flexShrink: 0
            }}
          />
        </Stack>

        {/* Status message */}
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 1,
            fontSize: '0.8rem',
            lineHeight: 1.3
          }}
        >
          {config.message}
        </Typography>

        {/* Footer with target info and time */}
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          {targetInfo && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                fontSize: '0.7rem',
                fontWeight: 500,
                backgroundColor: 'action.hover',
                px: 1,
                py: 0.25,
                borderRadius: 0.5
              }}
            >
              {targetInfo}
            </Typography>
          )}
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              fontSize: '0.7rem',
              ml: 'auto'
            }}
          >
            {timeAgo}
          </Typography>
        </Stack>
      </Box>
    </ButtonBase>
  );
};

export default NotificationItem;
