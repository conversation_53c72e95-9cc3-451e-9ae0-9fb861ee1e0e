'use client';

import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  datasetName: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export const DeleteConfirmationDialog = ({
  isOpen,
  datasetName,
  onConfirm,
  onCancel,
}: DeleteConfirmationDialogProps) => (
  <Dialog open={isOpen} onClose={onCancel}>
    <DialogTitle>Confirm Deletion</DialogTitle>
    <DialogContent>
      Are you sure you want to delete the dataset "{datasetName}"?
    </DialogContent>
    <DialogActions>
      <Button onClick={onCancel}>Cancel</Button>
      <Button onClick={onConfirm} color="error" variant="contained">
        Delete
      </Button>
    </DialogActions>
  </Dialog>
);
