'use client';

import React, { useState } from 'react';
import {
  IconButton,
  Collapse,
  Box,
  TableRow,
  TableCell,
  Stack,
  Button,
} from '@mui/material';
import Link from 'next/link';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { StyledTableRow, StyledTableCell, tableButtonStyles } from '../common/TablePresets';
import { Dataset } from './types';
import { DatasetFiles } from './DatasetFiles';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';
import { useDatasetActions } from './hooks/useDatasetActions';

interface DatasetRowProps {
  dataset: Dataset;
  onDelete: (dataset: Dataset) => void;
}

export const DatasetRow = ({ dataset, onDelete }: DatasetRowProps) => {
  const [open, setOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const { files, isLoading, fetchFiles, deleteDataset } = useDatasetActions(dataset.id);

  const handleDelete = async () => {
    const success = await deleteDataset();
    if (success) {
      onDelete(dataset);
    }
    setIsDeleteModalOpen(false);
  };

  const handleToggle = async () => {
    setOpen(!open);
    if (!open && files.length === 0) {
      await fetchFiles();
    }
  };

  return (
    <React.Fragment>
      <StyledTableRow>
        <StyledTableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={handleToggle}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </StyledTableCell>
        <StyledTableCell component="th" scope="row">
          {dataset.name}
        </StyledTableCell>
        <StyledTableCell>{dataset.description}</StyledTableCell>
        <StyledTableCell>
          <Stack direction="row" spacing={1} justifyContent="flex-end">
            <Link href={`/create-dataset?id=${dataset.id}`} passHref>
              <IconButton 
                size="small"
                sx={tableButtonStyles}
              >
                <EditIcon />
              </IconButton>
            </Link>
            <Button
              startIcon={<DeleteIcon />}
              onClick={() => setIsDeleteModalOpen(true)}
              sx={tableButtonStyles}
            >
              Delete
            </Button>
          </Stack>
        </StyledTableCell>
      </StyledTableRow>
      <TableRow>
        <TableCell style={{ padding: 0, border: 'none', background: 'none' }} colSpan={4}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <DatasetFiles files={files} isLoading={isLoading} />
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>

      <DeleteConfirmationDialog
        isOpen={isDeleteModalOpen}
        datasetName={dataset.name}
        onConfirm={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
      />
    </React.Fragment>
  );
};
