import React, { useState } from 'react';
import { Button } from '@mui/material';
import { processData } from '@/services/dataProcessingService';
import { JobConfirmationDialog } from './dialogs/JobConfirmationDialog';
import { JobSuccessDialog } from './dialogs/JobSuccessDialog';
import { ErrorDialog } from './dialogs/ErrorDialog';

interface ProcessDataProps {
  checkedTemplates: Record<string, any>;
  checkedDatasets: Record<string, any>;
}

const ProcessData = ({ checkedTemplates, checkedDatasets }: ProcessDataProps) => {
  const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
  const [isSuccessOpen, setIsSuccessOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedTemplate = Object.values(checkedTemplates)[0]?.template;
  const selectedDatasets = Object.values(checkedDatasets)
    .filter(d => d.checked)
    .map(d => d.dataset);

  const handleProcessClick = () => {
    setIsConfirmationOpen(true);
  };

  const handleConfirm = async () => {
    try {
      const result = await processData([selectedTemplate], selectedDatasets);
      setIsConfirmationOpen(false);

      if (result?.job) {
        setIsSuccessOpen(true);
      } else {
        setError('No jobs were created. Please try again.');
      }
    } catch (error) {
      setIsConfirmationOpen(false);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    }
  };

  const isDisabled = !selectedTemplate || selectedDatasets.length === 0;

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={handleProcessClick}
        disabled={isDisabled}
      >
        Process Data
      </Button>

      <JobConfirmationDialog
        open={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        onConfirm={handleConfirm}
        template={selectedTemplate}
        datasets={selectedDatasets}
      />

      <JobSuccessDialog
        open={isSuccessOpen}
        onClose={() => setIsSuccessOpen(false)}
      />

      <ErrorDialog
        open={!!error}
        onClose={() => setError(null)}
        error={error}
        title="Process Data Error"
      />
    </>
  );
};

export default ProcessData;