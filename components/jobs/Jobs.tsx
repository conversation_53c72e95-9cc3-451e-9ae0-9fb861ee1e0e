import React, { useState, useEffect } from 'react';
import { Box, Paper, Alert, Typography, Skeleton } from '@mui/material';
import dayjs from 'dayjs';
import JobsHeader from './JobsHeader';
import JobsTable from './JobsTable';
import JobsPagination from './JobsPagination';
import { useJobsState, useJobsDownload } from '../../lib/hooks/useJobsHooks';
import { JobsProps, JobFilters, JobItem } from '../../types/jobs';
import { getStatusDisplay } from '../../utils/jobUtils';

const Jobs: React.FC<JobsProps> = ({
  jobs,
  totalPages,
  currentPage,
  onPageChange,
  filters: initialFilters,
  onFilterChange: parentFilterChange
}) => {
  const [filters, setFilters] = useState<JobFilters>(initialFilters || {});
  const [isFilterApplied, setIsFilterApplied] = useState(false);

  const {
    jobList: jobListFromHook, // Renamed jobs to jobList<PERSON>romHook
    searchTerm,
    orderBy,
    order,
    handleSort,
    handleSearch,
    setJobList // Renamed setJobs to setJobList
  } = useJobsState(jobs); // Pass the initial jobs prop

  const {
    downloadingFiles,
    handleDownloadFileType,
    handleDownloadAll
  } = useJobsDownload();

  // Apply filters to the jobs from the hook
  const filteredJobs = React.useMemo(() => {
    const listToFilter = jobListFromHook || []; // Ensure we always have an array
    if (!isFilterApplied) return listToFilter;
    
    return listToFilter.filter((job: JobItem) => {
    // Filter by status - compare with display status not raw status
    if (filters.status) {
      const displayStatus = getStatusDisplay(job.status);
      if (displayStatus.text.toLowerCase() !== filters.status.toLowerCase()) {
        return false;
      }
    }
      
      // Filter by date range if present
      if (filters.dateRange) {
        const jobDate = new Date(job.created_at);
        
        if (filters.dateRange.from && jobDate < filters.dateRange.from) {
          return false;
        }
        
        if (filters.dateRange.to && jobDate > filters.dateRange.to) {
          return false;
        }
      }
      
      // Additional filters can be added here
      
      return true;
    });
  }, [jobListFromHook, filters, isFilterApplied]); // Update dependency

  // Handle filter changes
  const handleFilterChange = (filterName: string, value: any) => {
    if (filterName === 'reset') {
      setFilters({});
      setIsFilterApplied(false);
      if (parentFilterChange) {
        parentFilterChange('reset', null);
      }
      return;
    }
    
    setFilters(prev => {
      const newFilters = { ...prev, [filterName]: value };
      // If value is empty, remove the filter
      if (value === '') {
        delete newFilters[filterName as keyof JobFilters];
      }
      return newFilters;
    });
    
    setIsFilterApplied(true);
    
    // Propagate filter changes to parent if needed
    if (parentFilterChange) {
      parentFilterChange(filterName, value);
    }
  };

  // Get the isLoading status from props
  const isLoading = jobs.length === 0 && totalPages === 0;

  // Loading skeleton
  if (isLoading) {
    return (
      <Paper
        elevation={0}
        sx={{ p: 3, borderRadius: 2, boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}
      >
        <Box sx={{ mb: 3 }}>
          <Skeleton variant="rectangular" width="100%" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={40} />
        </Box>
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} variant="rectangular" width="100%" height={60} sx={{ mb: 1 }} />
        ))}
      </Paper>
    );
  }

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        borderRadius: 2,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        transition: 'all 0.3s ease'
      }}
    >
      <JobsHeader
        searchTerm={searchTerm}
        onSearch={handleSearch}
        filters={filters}
        onFilterChange={handleFilterChange}
      />
      
      {filteredJobs.length > 0 ? (
        <>
          <JobsTable
            jobs={filteredJobs}
            orderBy={orderBy}
            order={order}
            onSort={handleSort}
            downloadingFiles={downloadingFiles}
            onDownloadFileType={handleDownloadFileType}
            onDownloadAll={handleDownloadAll}
          />
          <JobsPagination
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={onPageChange}
          />
        </>
      ) : (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          {searchTerm || isFilterApplied ? (
            <Alert severity="info" sx={{ display: 'inline-flex', textAlign: 'left' }}>
              No jobs found with the current {searchTerm && isFilterApplied ? 'search term and filters' :
                                            searchTerm ? 'search term' : 'filters'}.
              Try adjusting your criteria.
            </Alert>
          ) : (
            <Typography variant="body1" color="text.secondary">
              No jobs available yet. Jobs will appear here once created.
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default Jobs;
