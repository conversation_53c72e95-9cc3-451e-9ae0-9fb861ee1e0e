import React, { memo } from 'react';
import { TableBody, TableSortLabel } from '@mui/material';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableRow
} from '../common/TablePresets';
import JobRow from './JobRow';
import { JobsTableProps } from '../../types/jobs';

const JobsTable: React.FC<JobsTableProps> = ({
  jobs,
  orderBy,
  order,
  onSort,
  downloadingFiles,
  onDownloadFileType,
  onDownloadAll
}) => {
  return (
    <StyledTableContainer role="region" aria-label="Jobs list" tabIndex={0}>
      <StyledTable>
        <StyledTableHead>
          <StyledTableRow>
            <StyledHeaderCell>
              <TableSortLabel
                active={orderBy === 'id'}
                direction={orderBy === 'id' ? order : 'asc'}
                onClick={() => onSort('id')}
              >
                ID
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell>
              <TableSortLabel
                active={orderBy === 'name'}
                direction={orderBy === 'name' ? order : 'asc'}
                onClick={() => onSort('name')}
              >
                Name
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell>
              <TableSortLabel
                active={orderBy === 'global_job_template'}
                direction={orderBy === 'global_job_template' ? order : 'asc'}
                onClick={() => onSort('global_job_template')}
              >
                Job Template
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell>
              Datasets
            </StyledHeaderCell>
            <StyledHeaderCell>
              <TableSortLabel
                active={orderBy === 'status'}
                direction={orderBy === 'status' ? order : 'asc'}
                onClick={() => onSort('status')}
              >
                Status
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell>
              <TableSortLabel
                active={orderBy === 'created_at'}
                direction={orderBy === 'created_at' ? order : 'asc'}
                onClick={() => onSort('created_at')}
              >
                Created At
              </TableSortLabel>
            </StyledHeaderCell>
            <StyledHeaderCell align="left">Available Downloads</StyledHeaderCell>
          </StyledTableRow>
        </StyledTableHead>
        <TableBody>
          {jobs.map((job) => ( // Renamed batch to job
            <JobRow
              key={job.id} // Use job.id
              job={job} // Pass job prop instead of batch
              downloadingFiles={downloadingFiles}
              onDownloadFileType={onDownloadFileType}
              onDownloadAll={onDownloadAll}
            />
          ))}
        </TableBody>
      </StyledTable>
    </StyledTableContainer>
  );
};

export default memo(JobsTable);
