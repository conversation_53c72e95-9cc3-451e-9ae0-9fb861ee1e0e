import React, { useState } from 'react';
import { Box, Typography, TextField, Button, Popover, Stack, FormControl, InputLabel, MenuItem, Select, IconButton } from '@mui/material';
import { Add as AddIcon, FilterList as FilterListIcon, Clear as ClearIcon } from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { JobsHeaderProps } from '../../types/jobs';
import dayjs from 'dayjs';

const JobsHeader: React.FC<JobsHeaderProps> = ({ searchTerm, onSearch, onFilterChange, filters }) => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const handleCreateJob = () => {
    router.push('/jobs/create');
  };

  const handleClearSearch = () => {
    // This assumes onSearch is set up to handle an empty string correctly
    onSearch({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          Jobs
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateJob}
        >
          Create Job
        </Button>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, alignItems: { xs: 'stretch', sm: 'center' } }}>
        <TextField
          size="small"
          variant="outlined"
          placeholder="Search jobs..."
          value={searchTerm}
          onChange={onSearch}
          fullWidth
          sx={{ maxWidth: { sm: '350px' } }}
          InputProps={{
            'aria-label': 'Search jobs',
            endAdornment: searchTerm ? (
              <IconButton
                size="small"
                onClick={handleClearSearch}
                aria-label="Clear search"
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            ) : null
          }}
        />

        <Button
          variant="outlined"
          startIcon={<FilterListIcon />}
          onClick={handleFilterClick}
          sx={{ minWidth: '100px' }}
        >
          Filters
        </Button>
      </Box>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Stack spacing={2} sx={{ p: 3, width: '300px' }}>
          <Typography variant="subtitle1" fontWeight={500}>Filters</Typography>
          <FormControl size="small" fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters?.status || ''}
              onChange={(e) => onFilterChange && onFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="">All Statuses</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="in progress">In Progress</MenuItem>
              <MenuItem value="failed">Failed</MenuItem>
              <MenuItem value="queued">Queued</MenuItem>
              <MenuItem value="partially completed">Partially Completed</MenuItem>
            </Select>
          </FormControl>

          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>Date Range</Typography>

          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <DatePicker
                label="From Date"
                value={filters?.dateRange?.from ? dayjs(filters.dateRange.from) : null}
                onChange={(date) => {
                  onFilterChange && onFilterChange('dateRange', {
                    ...(filters?.dateRange || {}),
                    from: date ? date.toDate() : null
                  });
                }}
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true
                  }
                }}
              />

              <DatePicker
                label="To Date"
                value={filters?.dateRange?.to ? dayjs(filters.dateRange.to) : null}
                onChange={(date) => {
                  onFilterChange && onFilterChange('dateRange', {
                    ...(filters?.dateRange || {}),
                    to: date ? date.toDate() : null
                  });
                }}
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true
                  }
                }}
              />
            </Stack>
          </LocalizationProvider>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Button
              variant="text"
              onClick={() => {
                onFilterChange && onFilterChange('reset', null);
                handleFilterClose();
              }}
            >
              Reset All
            </Button>
            <Button
              variant="contained"
              onClick={handleFilterClose}
            >
              Apply
            </Button>
          </Box>
        </Stack>
      </Popover>
    </Box>
  );
};

export default JobsHeader;
