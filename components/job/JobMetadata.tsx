import React from 'react';
import { Grid, Box, Typography } from '@mui/material';
import { Job, Task } from '../../types/job'; // Changed Batch to Job
import { formatDate } from '../../utils/jobUtils';

interface JobMetadataProps {
  job: Job; // Changed batch to job
  tasks: Task[];
}

const JobMetadata: React.FC<JobMetadataProps> = ({ job, tasks }) => { // Changed batch to job
  return (
    <Grid container spacing={2} sx={{ px: 2 }}>
      <Grid item xs={12} sm={6} md={3}>
        <Box>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Job Template
          </Typography>
          <Typography variant="body1">
            {job.template_name || job.tasks?.[0]?.global_job_template?.name || 'N/A'} {/* Changed batch to job and batch.jobs to job.tasks */}
          </Typography>
        </Box>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Box>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Created
          </Typography>
          <Typography variant="body1">
            {formatDate(job.created_at)} {/* Changed batch.created_at to job.created_at */}
          </Typography>
        </Box>
      </Grid>

      
    </Grid>
  );
};

export default JobMetadata;
