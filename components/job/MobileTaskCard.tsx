import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Stack,
  IconButton,
  Divider,
  Box,
  Grid,
  Tooltip,
  CircularProgress
} from '@mui/material';
import SupportIcon from '../common/SupportIcon';
import SupportIconWithStatus from '../common/SupportIconWithStatus';
import DownloadIcon from '../common/DownloadIcon';
import { Task } from '../../types/job';
import { formatDate, getStatusDisplay, getFileTypeLabel } from '../../utils/jobUtils';

interface MobileTaskCardProps {
  task: Task;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  onOpenTicketModal: (ticket: any) => void; // New prop for opening ticket modal
  downloadingFiles: Record<string, boolean>;
  jobId: string; // Changed batchId to jobId
}

const MobileTaskCard: React.FC<MobileTaskCardProps> = ({
  task,
  onDownloadFile,
  onDownloadTaskFiles,
  onOpenSupportDialog,
  onOpenTicketModal,
  downloadingFiles,
  jobId // Changed batchId to jobId
}) => {
  const fileTypes = ['kml', 'csv', 'pdf'];
  const status = getStatusDisplay(task.status, true);
  
  return (
    <Card sx={{ mb: 2, display: { xs: 'block', md: 'none' } }}>
      <CardContent>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="h6" component="div">
            Task #{task.id}
          </Typography>
          <Chip
            label={status.text}
            color={status.color as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        </Stack>
        
        <Box sx={{ mb: 1, maxWidth: '100%' }}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              lineHeight: 1.5,
              maxWidth: '100%'
            }}
          >
            <Box component="span" sx={{
              fontWeight: 'medium',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              flexShrink: 0,
              maxWidth: task.dataset?.description ? '120px' : '100%'
            }}>
              {task.dataset?.name || 'N/A'}
            </Box>
            {task.dataset?.description && (
              <>
                <Box component="span" sx={{ mx: 1, color: 'text.secondary', flexShrink: 0 }}>
                  —
                </Box>
                <Box component="span" sx={{
                  color: 'text.secondary',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flex: 1,
                  minWidth: 0
                }}>
                  {task.dataset.description}
                </Box>
              </>
            )}
          </Typography>
        </Box>
        
        <Typography variant="body2" sx={{ mb: 1 }}>
          Created: {formatDate(task.created_at)}
        </Typography>
        
        <Divider sx={{ my: 1 }} />
        
        <Typography variant="subtitle2" sx={{ mt: 1, mb: 1 }}>
          Available Files:
        </Typography>
        
        <Grid container spacing={1}>
          {fileTypes.map((type) => {
            const result = task.task_results?.find(r => 
              type === 'csv' ? 
                r.file_type?.toLowerCase() === 'csv_pva' :
                r.file_type?.toLowerCase() === type
            );
            
            if (!result) return null;
            
            const fileId = `${task.id}-${result.id}`;
            const isDownloading = downloadingFiles[fileId];
            
            return (
              <Grid item key={type}>
                <Tooltip title={`Download ${getFileTypeLabel(type)}`}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center'
                  }}>
                    <IconButton
                      size="small"
                      onClick={() => onDownloadFile(task.id, result)}
                      disabled={isDownloading}
                      aria-label={`Download ${getFileTypeLabel(type)}`}
                    >
                      {isDownloading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <DownloadIcon fileType={type} isMulti={false} />
                      )}
                    </IconButton>
                    <Typography variant="caption">
                      {getFileTypeLabel(type)}
                    </Typography>
                  </Box>
                </Tooltip>
              </Grid>
            );
          })}
        </Grid>
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'flex-end', gap: 1 }}>
        {task.task_results && task.task_results.length > 0 && (
          <Tooltip title="Download all files for this task">
            <IconButton
              size="medium"
              onClick={() => onDownloadTaskFiles(task.id)}
              disabled={!!downloadingFiles[`task-${task.id}`]}
              aria-label={`Download all files for task ${task.id}`}
              sx={{ 
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main'
                }
              }}
            >
              {downloadingFiles[`task-${task.id}`] ? (
                <CircularProgress size={20} />
              ) : (
                <DownloadIcon fileType="all" isMulti={false} />
              )}
            </IconButton>
          </Tooltip>
        )}
        <SupportIconWithStatus
          targetType="task"
          targetId={task.id}
          onTicketClick={onOpenTicketModal}
          onCreateTicket={() => onOpenSupportDialog(task.id, task.dataset?.name || '', jobId)}
          variant="icon"
          size="small"
        />
      </CardActions>
    </Card>
  );
};

export default MobileTaskCard;
