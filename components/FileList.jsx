import { SimpleTreeView, TreeItem } from '@mui/x-tree-view';
import { Typography, IconButton } from '@mui/material';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import FolderIcon from '@mui/icons-material/Folder';
const buildFileTree = (files) => {
  const tree = {};
  files.forEach(file => {
    const parts = file.fullPath.split('/').slice(1); // Remove the session ID
    let currentLevel = tree;
    parts.forEach((part, index) => {
      if (!currentLevel[part]) {
        currentLevel[part] = index === parts.length - 1 ? file : {};
      }
      currentLevel = currentLevel[part];
    });
  });
  return tree;
};

const renderTree = (nodes, path = '', onDelete) => (
  Object.entries(nodes).map(([key, value]) => {
    const isFile = value.hasOwnProperty('metadata');
    const currentPath = `${path}${key}`;
    
    return (
      <TreeItem
        itemId={currentPath}
        key={currentPath}
        label={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2">
              {isFile ? <InsertDriveFileIcon fontSize="small" /> : <FolderIcon fontSize="small" />} {key}
            </Typography>
            {isFile && (
              <IconButton size="small" onClick={() => onDelete(value.fullPath)}>
                <DeleteForeverIcon fontSize="small" />
              </IconButton>
            )}
          </div>
        }
      >
        {!isFile && renderTree(value, `${currentPath}/`, onDelete)}
      </TreeItem>
    );
  })
);

export function FileList({ files, onDelete }) {
  const fileTree = buildFileTree(files);

  return (
    <SimpleTreeView
    
      >
      {renderTree(fileTree, '', onDelete)}
    </SimpleTreeView>
  );
}