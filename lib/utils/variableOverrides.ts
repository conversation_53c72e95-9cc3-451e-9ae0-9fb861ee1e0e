interface VariableLike {
  name?: string;
  gui?: any;
  gui_config?: any;
  component_name?: string;
  links?: any[];
}

const PRIMITIVE_TYPES = new Set(['string', 'number', 'boolean']);

const cloneJson = <T>(value: T): T => {
  if (value === undefined || value === null) {
    return value as T;
  }
  return JSON.parse(JSON.stringify(value)) as T;
};

const getGuiConfig = (variable?: VariableLike) => {
  if (!variable) return undefined;
  return variable.gui ?? variable.gui_config ?? undefined;
};

const getComponentId = (variable?: VariableLike): string | undefined => {
  if (!variable) return undefined;
  const gui = getGuiConfig(variable);
  return (
    variable.component_name ||
    gui?.component_name ||
    gui?.component_id ||
    variable?.gui?.component_id ||
    variable?.gui_config?.component_id
  );
};

const flattenValueList = (value: any): any[] => {
  const result: any[] = [];
  const queue: any[] = Array.isArray(value) ? [...value] : [value];

  while (queue.length) {
    const item = queue.shift();
    if (Array.isArray(item)) {
      queue.unshift(...item);
    } else if (item !== null && item !== undefined) {
      result.push(item);
    }
  }

  return result;
};

const createDeduplicatedList = (values: any[]): any[] => {
  const seen = new Set<string>();
  const deduped: any[] = [];

  for (const value of values) {
    const type = typeof value;
    const key = type === 'object' ? `${type}:${JSON.stringify(value)}` : `${type}:${String(value)}`;
    if (!seen.has(key)) {
      seen.add(key);
      deduped.push(value);
    }
  }

  return deduped;
};

const orderByTemplateItems = (values: any[], templateVar?: VariableLike): any[] => {
  const gui = getGuiConfig(templateVar);
  const items = Array.isArray(gui?.items) ? gui.items : null;

  if (!items || items.length === 0) {
    return values;
  }

  const allowedOrder = items.map((item: any) => (typeof item === 'object' ? item.value : item));
  const allowedSet = new Set(values.map((v) => v));
  const ordered = allowedOrder.filter((itemValue: any) => allowedSet.has(itemValue));
  const leftovers = values.filter((itemValue: any) => !allowedOrder.includes(itemValue));

  return [...ordered, ...leftovers];
};

const sanitizeCheckboxGroupValue = (value: any, templateVar?: VariableLike): any[] => {
  if (!Array.isArray(value)) {
    if (value === null || value === undefined) {
      return [];
    }
    return [value];
  }

  const flattened = flattenValueList(value).filter((item) => {
    if (item === null || item === undefined) return false;
    const type = typeof item;
    return PRIMITIVE_TYPES.has(type) || type === 'object';
  });

  const deduped = createDeduplicatedList(flattened);
  return orderByTemplateItems(deduped, templateVar);
};

export const sanitizeOverrideValue = (rawValue: any, templateVar?: VariableLike): any => {
  const componentId = getComponentId(templateVar);

  if (componentId === 'CheckboxGroup') {
    return sanitizeCheckboxGroupValue(rawValue, templateVar);
  }

  return rawValue;
};

export const sanitizeVariableOverrideMap = (
  variableOverrides: Record<string, any>,
  {
    templateVars = [],
    currentVars = [],
  }: {
    templateVars?: VariableLike[];
    currentVars?: VariableLike[];
  } = {}
) => {
  const sanitized: Record<string, any> = {};

  for (const [variableName, rawValue] of Object.entries(variableOverrides)) {
    const templateVar = templateVars.find((v) => v?.name === variableName) ?? currentVars.find((v) => v?.name === variableName);
    sanitized[variableName] = sanitizeOverrideValue(rawValue, templateVar);
  }

  return sanitized;
};

export const deriveGuiConfig = (
  variableName: string,
  templateVars: VariableLike[] = [],
  currentVars: VariableLike[] = []
) => {
  const existing = currentVars.find((v) => v?.name === variableName);
  const templateVar = templateVars.find((v) => v?.name === variableName);
  const gui = getGuiConfig(existing) ?? getGuiConfig(templateVar);

  if (gui) {
    return cloneJson(gui);
  }

  return {
    component_id: 'TextInput',
    label: variableName,
    group: 'Variable Tree',
    order: 999,
  };
};

export const deriveLinks = (
  variableName: string,
  templateVars: VariableLike[] = [],
  currentVars: VariableLike[] = []
) => {
  const existing = currentVars.find((v) => v?.name === variableName);
  const templateVar = templateVars.find((v) => v?.name === variableName);

  return cloneJson(existing?.links ?? templateVar?.links ?? []);
};

export const deriveComponentName = (
  variableName: string,
  templateVars: VariableLike[] = [],
  currentVars: VariableLike[] = []
) => {
  const existing = currentVars.find((v) => v?.name === variableName);
  const templateVar = templateVars.find((v) => v?.name === variableName);

  return (
    existing?.component_name ||
    templateVar?.component_name ||
    getGuiConfig(templateVar)?.component_name ||
    getGuiConfig(existing)?.component_name ||
    getGuiConfig(templateVar)?.component_id ||
    getGuiConfig(existing)?.component_id
  );
};

export { cloneJson };
