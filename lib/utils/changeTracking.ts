import { VariableChange, VariableWithContext, VariableSaveContext } from '@/types/variable';
import { determineVariableSaveContext } from './variableState';

/**
 * Tracks changes to variables for batch saving
 */
export class VariableChangeTracker {
  private changes: Map<string, VariableChange> = new Map();

  /**
   * Build a composite key that uniquely identifies a change by context and variable name
   * Format: `${saveLevel}:${targetIdOrTemplate}:${variableName}`
   */
  static buildChangeKey(variableName: string, saveContext: VariableSaveContext): string {
    const level = saveContext.saveLevel;
    const target = level === 'template' ? 'template' : String(saveContext.targetId ?? 'unknown');
    return `${level}:${target}:${variableName}`;
  }

  addChange(
    variableName: string,
    oldValue: any,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset',
    action: 'set' | 'reset' = 'set'
  ): void {
    const saveContext = determineVariableSaveContext(originalVariable, nodeId, nodeType);
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);

    this.changes.set(key, {
      variableName,
      oldValue,
      newValue,
      saveContext,
      originalVariable,
      action
    });
  }

  getChanges(): VariableChange[] {
    return Array.from(this.changes.values());
  }

  hasChanges(): boolean {
    return this.changes.size > 0;
  }

  clearChanges(): void {
    this.changes.clear();
  }

  /**
   * Remove a change for a variable in a specific context. If no context is provided,
   * falls back to removing the template-level key for backward compatibility.
   */
  removeChange(variableName: string, saveContext?: VariableSaveContext): void {
    if (saveContext) {
      const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
      this.changes.delete(key);
      return;
    }
    // Backward compatibility: attempt to delete the template-level key
    const legacyKey = VariableChangeTracker.buildChangeKey(variableName, { saveLevel: 'template' });
    this.changes.delete(legacyKey);
  }
}

/**
 * Groups changes by their save context (template, category, dataset)
 */
export function groupChangesByContext(changes: VariableChange[]): Map<string, VariableChange[]> {
  const grouped = new Map<string, VariableChange[]>();
  
  for (const change of changes) {
    const key = change.saveContext.saveLevel === 'template' 
      ? 'template'
      : `${change.saveContext.saveLevel}-${change.saveContext.targetId}`;
    
    if (!grouped.has(key)) {
      grouped.set(key, []);
    }
    grouped.get(key)!.push(change);
  }
  
  return grouped;
}
