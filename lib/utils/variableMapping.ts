import { VariableWithContext } from '@/types/variable';
import { TemplateVariable } from '@/components/template/types/template';

/**
 * Mapping utilities to convert between VariableWithContext (Variable Tree API) 
 * and TemplateVariable (Input System) formats
 */

/**
 * Converts VariableWithContext from Variable Tree API to TemplateVariable format
 * that can be used with InputRenderer
 */
export function variableWithContextToTemplateVariable(
  variableWithContext: VariableWithContext,
  contextInfo?: {
    nodeId?: number;
    nodeType?: 'category' | 'dataset';
    nodeName?: string;
  }
): TemplateVariable {
  const templateVariable: TemplateVariable = {
    name: variableWithContext.name,
    data: variableWithContext.value,
    links: variableWithContext.links || [],
    component_name: variableWithContext.gui_config?.component_name,
    gui: variableWithContext.gui_config ? {
      component_id: variableWithContext.gui_config.component_id,
      group: variableWithContext.gui_config.group || 'Variable Tree',
      order: variableWithContext.gui_config.order || 0,
      label: variableWithContext.gui_config.label,
      tooltip: variableWithContext.gui_config.tooltip,
      min_value: variableWithContext.gui_config.min_value,
      max_value: variableWithContext.gui_config.max_value,
      max_digits: variableWithContext.gui_config.max_digits,
      max_chars: variableWithContext.gui_config.max_chars,
      integer: variableWithContext.gui_config.integer,
      items: variableWithContext.gui_config.items,
      value_checked: variableWithContext.gui_config.value_checked,
      value_unchecked: variableWithContext.gui_config.value_unchecked,
      min_checked: variableWithContext.gui_config.min_checked,
      max_checked: variableWithContext.gui_config.max_checked,
      msg_below_min: variableWithContext.gui_config.msg_below_min,
      dependson_var: variableWithContext.gui_config.dependson_var,
      dependson_val: variableWithContext.gui_config.dependson_val,
      axes: variableWithContext.gui_config.axes,
      visible_axes: variableWithContext.gui_config.visible_axes,
      unit: variableWithContext.gui_config.unit,
      process_type: variableWithContext.gui_config.process_type,
    } : undefined
  };

  return templateVariable;
}

/**
 * Converts TemplateVariable back to VariableWithContext format
 * Used when saving changes back to the Variable Tree
 */
export function templateVariableToVariableWithContext(
  templateVariable: TemplateVariable,
  originalContext: VariableWithContext
): VariableWithContext {
  return {
    ...originalContext,
    name: templateVariable.name,
    value: templateVariable.data,
    links: templateVariable.links,
    gui_config: templateVariable.gui ? {
      component_id: templateVariable.gui.component_id,
      component_name: templateVariable.component_name,
      group: templateVariable.gui.group,
      order: templateVariable.gui.order,
      label: templateVariable.gui.label,
      tooltip: templateVariable.gui.tooltip,
      min_value: templateVariable.gui.min_value,
      max_value: templateVariable.gui.max_value,
      max_digits: templateVariable.gui.max_digits,
      max_chars: templateVariable.gui.max_chars,
      integer: templateVariable.gui.integer,
      items: templateVariable.gui.items,
      value_checked: templateVariable.gui.value_checked,
      value_unchecked: templateVariable.gui.value_unchecked,
      min_checked: templateVariable.gui.min_checked,
      max_checked: templateVariable.gui.max_checked,
      msg_below_min: templateVariable.gui.msg_below_min,
      dependson_var: templateVariable.gui.dependson_var,
      dependson_val: templateVariable.gui.dependson_val,
      axes: templateVariable.gui.axes,
      visible_axes: templateVariable.gui.visible_axes,
      unit: templateVariable.gui.unit,
      process_type: templateVariable.gui.process_type,
    } : originalContext.gui_config
  };
}
