import { Template, GUIComponent } from '../../components/template/types/template';

export const api = {
  // Datasets
  async getDatasets() {
    const response = await fetch('/api/datasets')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch datasets')
    }
    return result
  },

  async getRecentDatasets(limit: number = 5) {
    const response = await fetch(`/api/datasets/recent?limit=${limit}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch recent datasets')
    }
    return result
  },

  async createDataset(data: any) {
    const response = await fetch('/api/datasets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create dataset')
    }
    return result
  },

  async deleteDataset(id: string) {
    const response = await fetch(`/api/datasets?id=${id}`, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete dataset')
    }
    return result
  },

  // Templates
  async getTemplates() {
    const response = await fetch('/api/templates')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch templates')
    }
    return result
  },
  async createTemplate(template: Partial<Template>): Promise<Template> {
    const response = await fetch('/api/templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(template),
    })

    const json = await response.json()

    if (!json.success) {
      throw new Error(json.error || 'Failed to create template')
    }

    return json.data
  },

  async deleteTemplate(id: string): Promise<void> {
    const response = await fetch(`/api/templates/${id}`, {
      method: 'DELETE',
    })

    const json = await response.json()

    if (!json.success) {
      throw new Error(json.error || 'Failed to delete template')
    }
  },
  // Files
  async getFiles() {
    const response = await fetch('/api/file')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch files')
    }
    return result
  },

  async uploadFile(formData: FormData) {
    const response = await fetch('/api/file', {
      method: 'POST',
      body: formData
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to upload file')
    }
    return result
  },

  async deleteFile(path: string) {
    const response = await fetch(`/api/file?path=${encodeURIComponent(path)}`, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete file')
    }
    return result
  },

  // Batches (shown as Jobs in frontend)
  async getJobs(page?: number, itemsPerPage?: number) {
    const params = new URLSearchParams()
    if (page) params.append('page', page.toString())
    if (itemsPerPage) params.append('itemsPerPage', itemsPerPage.toString())

    const response = await fetch(`/api/jobs?${params.toString()}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch jobs')
    }
    return result
  },

  async getRecentJobs(limit: number = 5) {
    const response = await fetch(`/api/jobs/recent?limit=${limit}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch recent jobs')
    }
    return result
  },

  async getJobById(id: string) {
    const response = await fetch(`/api/jobs/${id}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch job')
    }
    return result
  },

  async createJob(data: any) {
    const response = await fetch('/api/jobs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create job')
    }
    return result
  },

  // Jobs (shown as Tasks in frontend)
  async getTasksByJobId(jobId: string) {
    // Note: Still uses the old batchId parameter name for the API call
    const response = await fetch(`/api/tasks?jobId=${jobId}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch tasks for job')
    }
    return result
  },

  async createTask(data: any) {
    const response = await fetch('/api/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create task')
    }
    return result
  },

  async downloadTaskResults(taskId: string) {
    // Note: Still uses the old /api/jobs endpoint
    const response = await fetch(`/api/tasks/${taskId}/download`)
    if (!response.ok) {
      throw new Error('Failed to download task results')
    }
    return response
  },

  // Single Dataset
  async getDataset(id: string) {
    const response = await fetch(`/api/datasets/${id}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch dataset')
    }
    return result
  },

  async updateDataset({ id, ...data }: { id: string, [key: string]: any }) {
    const response = await fetch(`/api/datasets/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to update dataset')
    }
    return result
  },



  async getDatasetFiles(datasetId: string) {
    const response = await fetch(`/api/dataset-files?datasetId=${datasetId}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch dataset files')
    }
    return result
  },


 async getTemplate(id: string): Promise<Template> {
  const response = await fetch(`/api/templates/${id}`);
  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to fetch template');
  }

  return json.data;
},

async updateTemplate(id: string, template: Partial<Template>): Promise<Template> {
  const response = await fetch(`/api/templates/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(template),
  });

  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to update template');
  }

  return json.data;
},

async getGUIComponents(): Promise<GUIComponent[]> {
  const response = await fetch('/api/gui-components');
  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to fetch GUI components');
  }

  return json.data;
},

// Log Fields
async getLogFields() {
  const response = await fetch('/api/log-fields');
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch log fields');
  }

  return result.data;
},

// Tree API
async getTreeSkeleton(withCounts: boolean = true) {
  const response = await fetch(`/api/tree?withCounts=${withCounts}`);
  const result = await response.json();
  if (!response.ok) {
    throw new Error(result.error || 'Failed to fetch tree skeleton');
  }
  return result;
},

// Variable Nodes API
async getVarNodes(key: string) {
  const response = await fetch(`/api/var-nodes?key=${encodeURIComponent(key)}`);
  const result = await response.json();
  if (!response.ok) {
    throw new Error(result.error || 'Failed to fetch variable nodes');
  }
  return result;
},

// Job Effective Variables API
async getJobEffectiveVars(templateId: number, datasetIds: number[]) {
  const response = await fetch('/api/job/effective', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ templateId, datasetIds })
  });
  const result = await response.json();
  if (!response.ok) {
    throw new Error(result.error || 'Failed to get effective job variables');
  }
  return result;
},

// Tickets
async getTickets(filters: { status?: string; myTickets?: boolean } = {}) {
  const params = new URLSearchParams();
  if (filters.status) params.append('status', filters.status);
  if (filters.myTickets) params.append('myTickets', 'true');

  const response = await fetch(`/api/tickets?${params.toString()}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch tickets');
  }

  return result.data;
},

async getTicket(id: string) {
  const response = await fetch(`/api/tickets/${id}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch ticket');
  }

  return result.data;
},

async createTicket(data: {
  title: string;
  description?: string;
  priority?: string;
  assigneeId?: string;
  targets?: Array<{ target_type: string; target_id: number }>;
}) {
  const response = await fetch('/api/tickets', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to create ticket');
  }

  return result.data;
},

async updateTicket(id: string, data: {
  title?: string;
  description?: string;
  priority?: string;
  assigneeId?: string;
}) {
  const response = await fetch(`/api/tickets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to update ticket');
  }

  return result.data;
},

async updateTicketStatus(id: string, status: string) {
  const response = await fetch(`/api/tickets/${id}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status })
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to update ticket status');
  }

  return result.data;
},

async getTicketMessages(ticketId: string, page: number = 0, limit: number = 10) {
  const params = new URLSearchParams();
  if (page) params.append('page', page.toString());
  if (limit) params.append('limit', limit.toString());

  const response = await fetch(`/api/tickets/${ticketId}/messages?${params.toString()}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch ticket comments');
  }

  return result.data;
},

async addTicketMessage(ticketId: string, body: string) {
  const response = await fetch(`/api/tickets/${ticketId}/messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ body })
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to add comment');
  }

  return result.data;
},

async getTicketsByTarget(targetType: 'job' | 'task' | 'dataset', targetId: number, jobOnly: boolean = false) {
  const params = new URLSearchParams();
  params.append('target_type', targetType);
  params.append('target_id', targetId.toString());
  if (jobOnly) {
    params.append('job_only', 'true');
  }

  const response = await fetch(`/api/tickets/by-target?${params.toString()}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch tickets by target');
  }

  return result.data;
},

// Notifications
async getNotifications(limit: number = 50) {
  const response = await fetch(`/api/notifications?limit=${limit}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch notifications');
  }

  return result.data;
},

async markNotificationsAsSeen() {
  const response = await fetch('/api/notifications/mark-seen', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to mark notifications as seen');
  }

  return result.data;
},

// Variable Tree State Management
async updateCategoryVariables(categoryId: number, variableOverrides: { [key: string]: any }) {
  const response = await fetch(`/api/variable-tree/category/${categoryId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      variableOverrides
    })
  });
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to update category variables');
  }

  return result;
},

async updateDatasetVariables(datasetId: number, variableOverrides: { [key: string]: any }) {
  const response = await fetch(`/api/variable-tree/dataset/${datasetId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      variableOverrides
    })
  });
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to update dataset variables');
  }

  return result;
},

async updateTemplateVariables(templateId: number, variableOverrides: { [key: string]: any }) {
  const response = await fetch(`/api/variable-tree/template/${templateId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      variableOverrides
    })
  });
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to update template variables');
  }

  return result;
},

// Variable Tree Reset/Delete Functions
async resetTemplateVariable(templateId: number, variableName: string) {
  const response = await fetch(`/api/variable-tree/template/${templateId}?name=${encodeURIComponent(variableName)}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to reset template variable');
  }

  return result;
},

async resetCategoryVariable(categoryId: number, variableName: string) {
  const response = await fetch(`/api/variable-tree/category/${categoryId}?name=${encodeURIComponent(variableName)}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to reset category variable');
  }

  return result;
},

async resetDatasetVariable(datasetId: number, variableName: string) {
  const response = await fetch(`/api/variable-tree/dataset/${datasetId}?name=${encodeURIComponent(variableName)}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error || 'Failed to reset dataset variable');
  }

  return result;
}

}