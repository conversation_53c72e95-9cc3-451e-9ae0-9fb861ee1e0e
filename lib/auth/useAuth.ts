"use client"

import { useState, useEffect } from 'react'
import { createClient } from "@/utils/supabase/client"
import { useRouter } from "next/navigation"
import { useAuthStore } from '@/lib/stores/authStore'

export function useAuth() {
    const [loading, setLoading] = useState(true)
    const router = useRouter()
    const supabase = createClient()
    const { user, setUser } = useAuthStore()

    useEffect(() => {
        async function checkUser() {
            const { data: { user: supabaseUser } } = await supabase.auth.getUser()
            setUser(supabaseUser)
            if (!supabaseUser) {
                router.push("/login")
                return
            }
            setLoading(false)
        }
        
        // Listen for auth state changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
            setUser(session?.user ?? null)
        })

        checkUser()
        
        return () => {
            subscription.unsubscribe()
        }
    }, [])

    return { user, loading, supabase }
}
