import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

type RouteHandler = (
    userId: string, 
    request: Request, 
    context: { params: Record<string, string | string[]> }
) => Promise<NextResponse>;

export function withAuth(handler: <PERSON><PERSON><PERSON><PERSON>) {
    return async function(
        request: Request,
        context: { params: Record<string, string | string[]> }
    ) {
        try {
            // Check for API key authentication first (for testing/development)
            const apiKey = request.headers.get('x-api-key');
            const validApiKey = process.env.API_KEY;

            if (validApiKey && apiKey === validApiKey) {
                // Use a default test user ID when API key is used
                const testUserId = process.env.TEST_USER_ID || '6c62d0d6-9ee0-4952-a2b9-6506de27ee54';
                return handler(testUserId, request, context);
            }

            // Fall back to Supabase authentication
            const supabase = createClient();
            const { data: { user } } = await supabase.auth.getUser();

            if (!user?.id) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            return handler(user.id, request, context);
        } catch (error) {
            return NextResponse.json(
                { error: 'Internal Server Error' },
                { status: 500 }
            );
        }
    }
}