import { useQuery, useMutation } from '@tanstack/react-query'
import { Template, GUIComponent, TemplateVariable } from '../../components/template/types/template'
import { api } from '../services/api'
import { useMemo } from 'react'

export const useGetTemplateVars = (templateId: string) => {
  return useQuery<Template>({
    queryKey: ['template', templateId],
    queryFn: () => api.getTemplate(templateId),
    enabled: !!templateId,
    staleTime: 1000,
  })
}

export const useGetGUIComponents = () => {
  return useQuery<GUIComponent[]>({
    queryKey: ['gui_components'],
    queryFn: api.getGUIComponents,
  })
}

type MergedVar = TemplateVariable & {
  parameters?: GUIComponent['parameters']
}

export const useMergedTemplateVars = (templateId: string) => {
  const { data: template, isLoading: templateLoading } = useGetTemplateVars(templateId)
  const { data: guiComponents, isLoading: componentsLoading } = useGetGUIComponents()

  const mergedVars = useMemo(() => {
    if (!template?.vars || !guiComponents) return []

    return template.vars.map(templateVar => {
      if (!templateVar.gui?.component_id) {
        return templateVar as MergedVar
      }

      // Find matching GUI component definition and access its parameters
      const matchingGuiComponent = guiComponents.find(
        component => component.id === templateVar.gui?.component_id
      )

      if (!matchingGuiComponent) {
        return templateVar as MergedVar
      }

      return {
        ...templateVar,
        parameters: matchingGuiComponent.parameters,
        gui: {
          ...templateVar.gui,
          ...matchingGuiComponent.parameters,
        },
        data: templateVar.data ?? matchingGuiComponent.parameters?.defaultValue ?? ''
      } as MergedVar
    })
  }, [template?.vars, guiComponents])

  return {
    mergedVars,
    template_data: template?.template_data,
    isLoading: templateLoading || componentsLoading,
    error: null
  }
}

export const useSaveTemplateVars = () => {
  return useMutation({
    mutationFn: ({ templateId, updatedTemplate }: { templateId: string; updatedTemplate: Partial<Template> }) =>
      api.updateTemplate(templateId, updatedTemplate),
  })
}