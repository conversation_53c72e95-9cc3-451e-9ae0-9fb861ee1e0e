import { useState, useEffect, useCallback } from 'react';
import { VariableTreeResponse, VariableWithContext, VariableTreeNode } from '@/types/variable';

export interface UseVariableTreeOptions {
  templateId: number;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseVariableTreeReturn {
  data: VariableTreeResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getVariablesByName: (variableName: string) => VariableWithContext[];
  getVariableState: (variableName: string) => {
    primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    secondaryState?: 'active' | 'overridden' | 'not-set';
    counts: {
      active: number;
      overridden: number;
      notSet: number;
      definedHigher: number;
      total: number;
    };
    activeVariable: any;
    overriddenBy: string[];
  };
  getVariableStateForContext: (variableName: string, contextNodeId?: number, contextDatasetId?: number) => {
    primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    secondaryState?: 'active' | 'overridden' | 'not-set';
    counts: {
      active: number;
      overridden: number;
      notSet: number;
      definedHigher: number;
      total: number;
    };
    activeVariable: any;
    overriddenBy: string[];
  };
  // New helper to compute the inherited value for a context (value that would be active after reset)
  getInheritedValueForContext: (variableName: string, contextNodeId?: number, contextDatasetId?: number) => any | undefined;
  focusedVariable: string | null;
  focusVariable: (variableName: string) => void;
  unfocusVariable: () => void;
  isVariableFocused: (variableName: string) => boolean;
}

export function useVariableTree({
  templateId,
  enabled = true,
  refetchInterval
}: UseVariableTreeOptions): UseVariableTreeReturn {
  const [data, setData] = useState<VariableTreeResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [focusedVariable, setFocusedVariable] = useState<string | null>(null);

  const fetchVariableTree = useCallback(async () => {
    if (!enabled || !templateId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/variable-tree?templateId=${templateId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch variable tree');
      }

      const result: VariableTreeResponse = await response.json();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching variable tree:', err);
    } finally {
      setLoading(false);
    }
  }, [templateId, enabled]);

  // Initial fetch
  useEffect(() => {
    fetchVariableTree();
  }, [fetchVariableTree]);

  // Optional refetch interval
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchVariableTree, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchVariableTree, refetchInterval, enabled]);

  // Helper function to get all variables with a specific name across the tree
  const getVariablesByName = useCallback((variableName: string): VariableWithContext[] => {
    if (!data) return [];

    const variables: VariableWithContext[] = [];

    // Add template variables
    const templateVar = data.template_variables.find(v => v.name === variableName);
    if (templateVar) {
      variables.push(templateVar);
    }

    // Recursively search through tree nodes
    const searchNode = (node: VariableTreeNode) => {
      // Check node variables
      const nodeVar = node.variables.find(v => v.name === variableName);
      if (nodeVar) {
        // Ensure category variables have the required properties
        const categoryVariable: VariableWithContext = {
          ...nodeVar,
          source_level: 'Category',
          is_active: nodeVar.is_active ?? true,
          is_overridden: nodeVar.is_overridden ?? false,
          value: nodeVar.value ?? nodeVar.data
        };
        variables.push(categoryVariable);
      }

      // Check datasets
      if (node.datasets) {
        node.datasets.forEach(dataset => {
          const datasetVar = dataset.variables.find(v => v.name === variableName);
          if (datasetVar) {
            // Ensure dataset variables have the required properties
            const datasetVariable: VariableWithContext = {
              ...datasetVar,
              source_level: 'Dataset',
              is_active: datasetVar.is_active ?? true,
              is_overridden: datasetVar.is_overridden ?? false,
              value: datasetVar.value ?? datasetVar.data
            };
            variables.push(datasetVariable);
          }
        });
      }

      // Check children
      if (node.children) {
        node.children.forEach(searchNode);
      }
    };

    data.tree.forEach(searchNode);
    return variables;
  }, [data]);

  // Helper function to calculate variable state based on hierarchy override rules
  const getVariableState = useCallback((variableName: string) => {
    if (!data) {
      return {
        primaryState: 'not-set' as const,
        secondaryState: undefined,
        counts: { active: 0, overridden: 0, notSet: 0, definedHigher: 0, total: 0 },
        activeVariable: null,
        overriddenBy: []
      };
    }

    // Collect all variable instances with their hierarchy context
    const variableInstances: Array<{
      variable: any;
      level: number; // 0=template, 1=root-category, 2=sub-category, etc., 999=dataset
      path: string;
      nodeId?: number;
      datasetId?: number;
    }> = [];

    // Add template variable
    const templateVar = data.template_variables.find(v => v.name === variableName);
    if (templateVar) {
      variableInstances.push({
        variable: templateVar,
        level: 0,
        path: 'Template'
      });
    }

    // Recursively search through tree nodes
    const searchNode = (node: VariableTreeNode, level: number, parentPath: string = '') => {
      const currentPath = parentPath ? `${parentPath} > ${node.name}` : node.name;
      
      // Check node variables (categories)
      const nodeVar = node.variables.find(v => v.name === variableName);
      if (nodeVar) {
        variableInstances.push({
          variable: nodeVar,
          level: level,
          path: currentPath,
          nodeId: node.id
        });
      }

      // Check datasets (highest priority)
      if (node.datasets) {
        node.datasets.forEach(dataset => {
          const datasetVar = dataset.variables.find(v => v.name === variableName);
          if (datasetVar) {
            variableInstances.push({
              variable: datasetVar,
              level: 999, // Datasets always have highest priority
              path: `${currentPath} > ${dataset.name}`,
              nodeId: node.id,
              datasetId: dataset.id
            });
          }
        });
      }

      // Check children (increment level for deeper categories)
      if (node.children) {
        node.children.forEach(childNode => {
          searchNode(childNode, level + 1, currentPath);
        });
      }
    };

    data.tree.forEach(node => searchNode(node, 1)); // Start with level 1 for root categories

    // Sort by level (highest level = highest priority)
    variableInstances.sort((a, b) => b.level - a.level);

    const counts = {
      active: 0,
      overridden: 0,
      notSet: 0,
      definedHigher: 0,
      total: variableInstances.length
    };

    let activeVariable = null;
    const overriddenBy: string[] = [];

    if (variableInstances.length > 0) {
      // The first instance (highest level) is the active one
      activeVariable = variableInstances[0];
      counts.active = 1;
      
      // All others are overridden
      for (let i = 1; i < variableInstances.length; i++) {
        counts.overridden++;
        overriddenBy.push(variableInstances[i].path);
      }
    } else {
      counts.notSet = 1;
    }

    // Determine primary state (template-centric)
    let primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    let secondaryState: 'active' | 'overridden' | 'not-set' | undefined;

    // Special handling when template variable exists: evaluate from template perspective
    const templateIdx = variableInstances.findIndex(v => v.level === 0);
    if (templateIdx >= 0) {
      const templateIsActive = templateIdx === 0; // template instance is highest priority

      if (templateIsActive) {
        // Template value is currently the active one
        primaryState = 'active';
        if (counts.overridden > 0) {
          secondaryState = 'overridden';
        }
      } else {
        // Template value is overridden by deeper level (category/dataset)
        primaryState = 'overridden';
        // Kein Sekundärstatus für Template, da von tieferer Ebene überschrieben
        secondaryState = undefined;
      }
    } else {
      // No template variable → fallback to previous logic
      if (counts.active > 0) {
        primaryState = 'active';
        if (counts.overridden > 0) {
          secondaryState = 'overridden';
        }
      } else if (counts.overridden > 0) {
        primaryState = 'overridden';
      } else {
        primaryState = 'not-set';
      }
    }

    return {
      primaryState,
      secondaryState,
      counts,
      activeVariable,
      overriddenBy
    };
  }, [data]);

  // Helper function to find the path to a specific node/dataset
  const findNodePath = useCallback((targetNodeId?: number, targetDatasetId?: number): VariableTreeNode[] => {
    if (!data) return [];

    const path: VariableTreeNode[] = [];

    const searchNode = (node: VariableTreeNode): boolean => {
      // Check if this is the target node
      if (targetDatasetId) {
        // Looking for a dataset
        if (node.datasets?.some(dataset => dataset.id === targetDatasetId)) {
          path.push(node);
          return true;
        }
      } else if (targetNodeId && node.id === targetNodeId) {
        // Looking for a category node
        path.push(node);
        return true;
      }

      // Search in children
      if (node.children) {
        for (const child of node.children) {
          if (searchNode(child)) {
            path.unshift(node); // Add parent to beginning of path
            return true;
          }
        }
      }

      return false;
    };

    data.tree.forEach(node => searchNode(node));
    return path;
  }, [data]);

  // Helper function to calculate variable state for a specific context (node/dataset)
  const getVariableStateForContext = useCallback((variableName: string, contextNodeId?: number, contextDatasetId?: number) => {
    if (!data) {
      return {
        primaryState: 'not-set' as const,
        secondaryState: undefined,
        counts: { active: 0, overridden: 0, notSet: 0, definedHigher: 0, total: 0 },
        activeVariable: null,
        overriddenBy: []
      };
    }

    // Global (template) context: compute using template-centric logic across entire tree
    if (contextNodeId === undefined && contextDatasetId === undefined) {
      return getVariableState(variableName);
    }

    // Find the path to the current context
    const contextPath = findNodePath(contextNodeId, contextDatasetId);
    
    // Get all variable instances that are relevant to this context
    const variableInstances: Array<{
      variable: any;
      level: number;
      path: string;
      nodeId?: number;
      datasetId?: number;
    }> = [];

    // Add template variable (always visible)
    const templateVar = data.template_variables.find(v => v.name === variableName);
    if (templateVar) {
      variableInstances.push({
        variable: templateVar,
        level: 0,
        path: 'Template'
      });
    }

    // Add variables from the hierarchy path (parent categories)
    contextPath.forEach((node, index) => {
      const nodeVar = node.variables.find(v => v.name === variableName);
      if (nodeVar) {
        const pathSegments = contextPath.slice(0, index + 1).map(n => n.name);
        variableInstances.push({
          variable: nodeVar,
          level: index + 1,
          path: pathSegments.join(' > '),
          nodeId: node.id
        });
      }
    });

    // Add variable from the current context (if it's a dataset)
    if (contextDatasetId && contextPath.length > 0) {
      const parentNode = contextPath[contextPath.length - 1];
      const targetDataset = parentNode.datasets?.find(d => d.id === contextDatasetId);
      if (targetDataset) {
        const datasetVar = targetDataset.variables.find(v => v.name === variableName);
        if (datasetVar) {
          const pathSegments = [...contextPath.map(n => n.name), targetDataset.name];
          variableInstances.push({
            variable: datasetVar,
            level: 999, // Datasets have highest priority
            path: pathSegments.join(' > '),
            nodeId: parentNode.id,
            datasetId: targetDataset.id
          });
        }
      }
    }

    // Add variables from children (only if we're looking at a category, not a dataset)
    if (!contextDatasetId && contextPath.length > 0) {
      const currentNode = contextPath[contextPath.length - 1];
      
      const searchSubtree = (node: VariableTreeNode, level: number, parentPath: string) => {
        // Check datasets in this node
        if (node.datasets) {
          node.datasets.forEach(dataset => {
            const datasetVar = dataset.variables.find(v => v.name === variableName);
            if (datasetVar) {
              variableInstances.push({
                variable: datasetVar,
                level: 999,
                path: `${parentPath} > ${dataset.name}`,
                nodeId: node.id,
                datasetId: dataset.id
              });
            }
          });
        }

        // Check children
        if (node.children) {
          node.children.forEach(child => {
            const childVar = child.variables.find(v => v.name === variableName);
            if (childVar) {
              variableInstances.push({
                variable: childVar,
                level: level + 1,
                path: `${parentPath} > ${child.name}`,
                nodeId: child.id
              });
            }
            searchSubtree(child, level + 1, `${parentPath} > ${child.name}`);
          });
        }
      };

      // Include dataset variables directly under the current category
      if (currentNode.datasets) {
        currentNode.datasets.forEach(dataset => {
          const datasetVar = dataset.variables.find(v => v.name === variableName);
          if (datasetVar) {
            const pathSegments = [...contextPath.map(n => n.name), dataset.name];
            variableInstances.push({
              variable: datasetVar,
              level: 999, // Ensure datasets outrank any category-level variables
              path: pathSegments.join(' > '),
              nodeId: currentNode.id,
              datasetId: dataset.id
            });
          }
        });
      }

      // Include child categories and their subtrees
      if (currentNode.children) {
        currentNode.children.forEach(child => {
          const childVar = child.variables.find(v => v.name === variableName);
          if (childVar) {
            const pathSegments = [...contextPath.map(n => n.name), child.name];
            variableInstances.push({
              variable: childVar,
              level: contextPath.length + 1,
              path: pathSegments.join(' > '),
              nodeId: child.id
            });
          }
          searchSubtree(child, contextPath.length + 1, [...contextPath.map(n => n.name), child.name].join(' > '));
        });
      }
    }

    // Sort by level (highest level = highest priority)
    variableInstances.sort((a, b) => b.level - a.level);

    // Find the variable instance for the current context
    let contextVariable = null as null | { variable: any; level: number; path: string; nodeId?: number; datasetId?: number };
    if (contextDatasetId !== undefined) {
      contextVariable = variableInstances.find(v => v.datasetId === contextDatasetId) || null;
    } else if (contextNodeId !== undefined) {
      contextVariable = variableInstances.find(v => v.nodeId === contextNodeId && !v.datasetId) || null;
    }

    // Find the globally active variable (highest level)
    const globallyActiveVariable = variableInstances.length > 0 ? variableInstances[0] : null;

    const counts = {
      active: 0,
      overridden: 0,
      notSet: 0,
      definedHigher: 0,
      total: variableInstances.length
    };

    let primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    let secondaryState: 'active' | 'overridden' | 'not-set' | undefined;
    const overriddenBy: string[] = [];

    if (!contextVariable) {
      // Variable is not defined in this context
      if (globallyActiveVariable) {
        primaryState = 'defined-higher';
        counts.definedHigher = 1;
      } else {
        primaryState = 'not-set';
        counts.notSet = 1;
      }
    } else {
      // Variable is defined in this context
      if (globallyActiveVariable && 
          ((contextVariable.nodeId === globallyActiveVariable.nodeId && contextVariable.datasetId === globallyActiveVariable.datasetId) ||
           (contextVariable.nodeId === globallyActiveVariable.nodeId && !contextVariable.datasetId && !globallyActiveVariable.datasetId))) {
        // This variable is the globally active one
        primaryState = 'active';
        counts.active = 1;
        
        // Check if there are overridden variables
        const overriddenVariables = variableInstances.slice(1);
        if (overriddenVariables.length > 0) {
          secondaryState = 'overridden';
          counts.overridden = overriddenVariables.length;
          overriddenBy.push(...overriddenVariables.map(v => v.path));
        }
      } else {
        // This variable is overridden by a higher level
        primaryState = 'overridden';
        counts.overridden = 1;
        if (globallyActiveVariable) {
          overriddenBy.push(globallyActiveVariable.path);
        }
      }
    }

    return {
      primaryState,
      secondaryState,
      counts,
      activeVariable: globallyActiveVariable,
      overriddenBy
    };
  }, [data]);

  // Focus management functions
  const focusVariable = useCallback((variableName: string) => {
    setFocusedVariable(variableName);
  }, []);

  const unfocusVariable = useCallback(() => {
    setFocusedVariable(null);
  }, []);

  const isVariableFocused = useCallback((variableName: string) => {
    return focusedVariable === variableName;
  }, [focusedVariable]);

  // Helper to compute the inherited value for a context (value after reset)
  const getInheritedValueForContext = useCallback((variableName: string, contextNodeId?: number, contextDatasetId?: number) => {
    if (!data) return undefined;

    // Reuse logic from getVariableStateForContext to assemble instances
    const contextPath = findNodePath(contextNodeId, contextDatasetId);
    type Instance = { variable: any; level: number; path: string; nodeId?: number; datasetId?: number };
    const instances: Instance[] = [];

    const templateVar = data.template_variables.find(v => v.name === variableName);
    if (templateVar) {
      instances.push({ variable: templateVar, level: 0, path: 'Template' });
    }

    contextPath.forEach((node, index) => {
      const nodeVar = node.variables.find(v => v.name === variableName);
      if (nodeVar) {
        const pathSegments = contextPath.slice(0, index + 1).map(n => n.name);
        instances.push({ variable: nodeVar, level: index + 1, path: pathSegments.join(' > '), nodeId: node.id });
      }
    });

    if (contextDatasetId && contextPath.length > 0) {
      const parentNode = contextPath[contextPath.length - 1];
      const targetDataset = parentNode.datasets?.find(d => d.id === contextDatasetId);
      if (targetDataset) {
        const datasetVar = targetDataset.variables.find(v => v.name === variableName);
        if (datasetVar) {
          const pathSegments = [...contextPath.map(n => n.name), targetDataset.name];
          instances.push({ variable: datasetVar, level: 999, path: pathSegments.join(' > '), nodeId: parentNode.id, datasetId: targetDataset.id });
        }
      }
    }

    // Also gather from children, as they can override higher-levels in global selection logic
    if (!contextDatasetId && contextPath.length > 0) {
      const currentNode = contextPath[contextPath.length - 1];
      const searchSubtree = (node: VariableTreeNode, level: number, parentPath: string) => {
        if (node.datasets) {
          node.datasets.forEach(dataset => {
            const datasetVar = dataset.variables.find(v => v.name === variableName);
            if (datasetVar) {
              instances.push({ variable: datasetVar, level: 999, path: `${parentPath} > ${dataset.name}`, nodeId: node.id, datasetId: dataset.id });
            }
          });
        }
        if (node.children) {
          node.children.forEach(child => {
            const childVar = child.variables.find(v => v.name === variableName);
            if (childVar) {
              instances.push({ variable: childVar, level: level + 1, path: `${parentPath} > ${child.name}`, nodeId: child.id });
            }
            searchSubtree(child, level + 1, `${parentPath} > ${child.name}`);
          });
        }
      };
      if (currentNode.children) {
        currentNode.children.forEach(child => {
          const childVar = child.variables.find(v => v.name === variableName);
          if (childVar) {
            const pathSegments = [...contextPath.map(n => n.name), child.name];
            instances.push({ variable: childVar, level: contextPath.length + 1, path: pathSegments.join(' > '), nodeId: child.id });
          }
          searchSubtree(child, contextPath.length + 1, [...contextPath.map(n => n.name), child.name].join(' > '));
        });
      }
    }

    instances.sort((a, b) => b.level - a.level);

    // Identify context instance to exclude
    const isContextInstance = (inst: Instance) => {
      if (contextDatasetId) return inst.datasetId === contextDatasetId;
      if (contextNodeId) return inst.nodeId === contextNodeId && !inst.datasetId;
      return false;
    };

    const inherited = instances.find(inst => !isContextInstance(inst));
    return inherited ? (inherited.variable.value ?? inherited.variable.data) : undefined;
  }, [data, findNodePath]);

  return {
    data,
    loading,
    error,
    refetch: fetchVariableTree,
    getVariablesByName,
    getVariableState,
    getVariableStateForContext,
    getInheritedValueForContext,
    focusedVariable,
    focusVariable,
    unfocusVariable,
    isVariableFocused
  };
}
