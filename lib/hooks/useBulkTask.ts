import { useState, useCallback, useEffect } from 'react';
import { bulkTaskError, isbulkTaskError } from '@/lib/errors/BulkTaskError';
import { useBulkTaskStatus } from './useBulkTaskStatus';

interface UsebulkTaskOptions {
  onComplete?: (jobId: string) => void;
  onError?: (error: Error) => void;
}

interface bulkTaskState {
  jobId: string | null;
  isProcessing: boolean;
  error: Error | null;
}

export function usebulkTask(options: UsebulkTaskOptions = {}) {
  const [state, setState] = useState<bulkTaskState>({
    jobId: null,
    isProcessing: false,
    error: null
  });

  // Use job status hook for polling
  const { status, error: statusError } = useBulkTaskStatus(state.jobId);

  // Handle status changes
  useEffect(() => {
    if (status === 'complete' && state.jobId) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: null
      }));
      options.onComplete?.(state.jobId);
    } else if (status === 'error' && statusError) {
      const error = new Error(statusError);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error
      }));
      options.onError?.(error);
    }
  }, [status, statusError, state.jobId, options]);

  const createMultiKML = useCallback(async (taskIds: number[]) => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null
      }));

      const response = await fetch('/api/tasks/bulk/kml', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskIds }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw isbulkTaskError(data)
          ? new bulkTaskError(data.code, data.message, data.details)
          : new Error(data.error || 'Failed to create Multi-KML job');
      }

      setState(prev => ({
        ...prev,
        jobId: data.job.id
      }));

      return data.job;
    } catch (error) {
      const finalError = isbulkTaskError(error)
        ? error
        : bulkTaskError.unknown(error);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: finalError
      }));

      options.onError?.(finalError);
      throw finalError;
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({
      jobId: null,
      isProcessing: false,
      error: null
    });
  }, []);

  return {
    createMultiKML,
    reset,
    jobId: state.jobId,
    isProcessing: state.isProcessing || status === 'processing',
    error: state.error,
    status
  };
}

export default usebulkTask;