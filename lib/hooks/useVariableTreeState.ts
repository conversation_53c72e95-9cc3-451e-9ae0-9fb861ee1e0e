import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { VariableWithContext } from '@/types/variable';
import { VariableChangeTracker, groupChangesByContext } from '@/lib/utils/changeTracking';
import { api } from '@/lib/services/api';
import { determineVariableSaveContext } from '@/lib/utils/variableState';

export interface UseVariableTreeStateOptions {
  templateId: number;
  onSaveSuccess?: () => void;
  onSaveError?: (error: string) => void;
}

export interface UseVariableTreeStateReturn {
  // State management
  variableChanges: Map<string, any>;
  hasChanges: boolean;

  // Actions
  updateVariable: (variableName: string, newValue: any, originalVariable: VariableWithContext, nodeId?: number, nodeType?: 'category' | 'dataset') => void;
  resetVariable: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', originalVariable?: VariableWithContext) => void;
  resetAllChanges: () => void;
  saveChanges: () => Promise<void>;

  // Reset to inherited functionality
  resetToInherited: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', inheritedValue?: any) => Promise<void>;

  // Status
  isSaving: boolean;
  saveError: string | null;
  isResetting: boolean;
  resetError: string | null;

  // Helpers
  getVariableValue: (variableName: string, originalValue: any, nodeId?: number, nodeType?: 'category' | 'dataset') => any;
  isVariableChanged: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => boolean;
  isVariableResetPending: (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => boolean;
}

/**
 * Hook for managing Variable Tree state changes and saving
 */
export function useVariableTreeState({
  templateId,
  onSaveSuccess,
  onSaveError
}: UseVariableTreeStateOptions): UseVariableTreeStateReturn {
  const [variableChanges, setVariableChanges] = useState<Map<string, any>>(new Map());
  const [changeTracker] = useState(() => new VariableChangeTracker());
  const queryClient = useQueryClient();

  // React Query mutations for each save context
  const updateTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableOverrides }: { templateId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateTemplateVariables(templateId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableOverrides }: { categoryId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateCategoryVariables(categoryId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const updateDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableOverrides }: { datasetId: number; variableOverrides: { [key: string]: any } }) =>
      api.updateDatasetVariables(datasetId, variableOverrides),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Reset mutations for removing variable overrides
  const resetTemplateMutation = useMutation({
    mutationFn: ({ templateId, variableName }: { templateId: number; variableName: string }) =>
      api.resetTemplateVariable(templateId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetCategoryMutation = useMutation({
    mutationFn: ({ categoryId, variableName }: { categoryId: number; variableName: string }) =>
      api.resetCategoryVariable(categoryId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  const resetDatasetMutation = useMutation({
    mutationFn: ({ datasetId, variableName }: { datasetId: number; variableName: string }) =>
      api.resetDatasetVariable(datasetId, variableName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variable-tree', templateId] });
    }
  });

  // Compute overall saving state and error from mutations
  const isSaving = updateTemplateMutation.isPending || updateCategoryMutation.isPending || updateDatasetMutation.isPending;
  const saveError = updateTemplateMutation.error?.message || updateCategoryMutation.error?.message || updateDatasetMutation.error?.message || null;

  // Compute overall resetting state and error from reset mutations
  const isResetting = resetTemplateMutation.isPending || resetCategoryMutation.isPending || resetDatasetMutation.isPending;
  const resetError = resetTemplateMutation.error?.message || resetCategoryMutation.error?.message || resetDatasetMutation.error?.message || null;

  // Update a variable value
  const updateVariable = useCallback((
    variableName: string,
    newValue: any,
    originalVariable: VariableWithContext,
    nodeId?: number,
    nodeType?: 'category' | 'dataset'
  ) => {
    const saveContext = determineVariableSaveContext(originalVariable, nodeId, nodeType);

    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    setVariableChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.set(key, newValue);
      return newChanges;
    });

    // Track the change for saving
    changeTracker.addChange(
      variableName,
      originalVariable.value,
      newValue,
      originalVariable,
      nodeId,
      nodeType
    );

    // Clear any previous save error (mutations handle their own error state)
  }, [changeTracker]);

  // Reset a single variable
  const resetVariable = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', originalVariable?: VariableWithContext) => {
    // Build the same composite key used when updating
    const saveContext = originalVariable
      ? determineVariableSaveContext(originalVariable, nodeId, nodeType)
      : ({ saveLevel: (nodeType ?? 'template'), targetId: nodeId } as const);
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);

    setVariableChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.delete(key);
      return newChanges;
    });

    changeTracker.removeChange(variableName, saveContext);
  }, [changeTracker]);

  // Reset all changes
  const resetAllChanges = useCallback(() => {
    setVariableChanges(new Map());
    changeTracker.clearChanges();
    // Reset mutation states
    updateTemplateMutation.reset();
    updateCategoryMutation.reset();
    updateDatasetMutation.reset();
  }, [changeTracker, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation]);

  // Get the current value for a variable (changed or original)
  const getVariableValue = useCallback((variableName: string, originalValue: any, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    const saveContext = { saveLevel: nodeType ?? 'template', targetId: nodeId } as const;
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    return variableChanges.has(key) ? variableChanges.get(key) : originalValue;
  }, [variableChanges]);

  // Check if a variable has been changed
  const isVariableChanged = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    const saveContext = { saveLevel: nodeType ?? 'template', targetId: nodeId } as const;
    const key = VariableChangeTracker.buildChangeKey(variableName, saveContext);
    return variableChanges.has(key);
  }, [variableChanges]);

  // Check if there are any changes (includes pending resets tracked in changeTracker)
  const hasChanges = useMemo(() => {
    return variableChanges.size > 0 || changeTracker.hasChanges();
  }, [variableChanges, changeTracker]);

  // Track whether a variable has a pending reset in the change tracker
  const isVariableResetPending = useCallback((variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset') => {
    return changeTracker
      .getChanges()
      .some(c => c.variableName === variableName
        && c.action === 'reset'
        && ((nodeType ?? 'template') === c.saveContext.saveLevel)
        && ((nodeType ? nodeId : undefined) === c.saveContext.targetId)
      );
  }, [changeTracker]);


  // Save all changes using react-query mutations
  const saveChanges = useCallback(async () => {
    if (!hasChanges) return;

    try {
      const changes = changeTracker.getChanges();

      // Group changes by save context
      const changesByContext = groupChangesByContext(changes);

      // Save each group using appropriate mutation
      const savePromises: Promise<any>[] = [];

      changesByContext.forEach((contextChanges, contextKey) => {
        let saveLevel: string;
        let targetId: string | undefined;

        if (contextKey === 'template') {
          saveLevel = 'template';
          targetId = undefined;
        } else {
          [saveLevel, targetId] = contextKey.split('-');
        }

        // Separate set vs reset actions
        const setChanges = contextChanges.filter(c => c.action !== 'reset');
        const resetChanges = contextChanges.filter(c => c.action === 'reset');

        // 1) Apply set/override updates in bulk
        if (setChanges.length > 0) {
          const variableOverrides: { [key: string]: any } = {};
          for (const change of setChanges) {
            variableOverrides[change.variableName] = change.newValue;
          }

          if (saveLevel === 'template') {
            console.log('🔄 Saving to TEMPLATE level (set):', { templateId, variableOverrides });
            savePromises.push(updateTemplateMutation.mutateAsync({ templateId, variableOverrides }));
          } else if (saveLevel === 'category' && targetId && targetId !== 'template') {
            console.log('🔄 Saving to CATEGORY level (set):', { categoryId: parseInt(targetId), variableOverrides });
            savePromises.push(updateCategoryMutation.mutateAsync({ categoryId: parseInt(targetId), variableOverrides }));
          } else if (saveLevel === 'dataset' && targetId && targetId !== 'template') {
            console.log('🔄 Saving to DATASET level (set):', { datasetId: parseInt(targetId), variableOverrides });
            savePromises.push(updateDatasetMutation.mutateAsync({ datasetId: parseInt(targetId), variableOverrides }));
          }
        }

        // 2) Apply resets individually (API requires variableName)
        for (const resetChange of resetChanges) {
          if (saveLevel === 'template') {
            console.log('🗑️ Reset at TEMPLATE level:', { templateId, variableName: resetChange.variableName });
            savePromises.push(resetTemplateMutation.mutateAsync({ templateId, variableName: resetChange.variableName }));
          } else if (saveLevel === 'category' && targetId) {
            console.log('🗑️ Reset at CATEGORY level:', { categoryId: parseInt(targetId), variableName: resetChange.variableName });
            savePromises.push(resetCategoryMutation.mutateAsync({ categoryId: parseInt(targetId), variableName: resetChange.variableName }));
          } else if (saveLevel === 'dataset' && targetId) {
            console.log('🗑️ Reset at DATASET level:', { datasetId: parseInt(targetId), variableName: resetChange.variableName });
            savePromises.push(resetDatasetMutation.mutateAsync({ datasetId: parseInt(targetId), variableName: resetChange.variableName }));
          }
        }
      });

      // Wait for all mutations to complete
      await Promise.all(savePromises);

      // Clear changes after successful save
      resetAllChanges();
      onSaveSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save changes';
      onSaveError?.(errorMessage);
    }
  }, [hasChanges, changeTracker, templateId, resetAllChanges, onSaveSuccess, onSaveError, updateTemplateMutation, updateCategoryMutation, updateDatasetMutation]);

  // Reset to inherited (LOCAL ONLY). Mark a pending reset and restore inherited value locally.
  const resetToInherited = useCallback(async (variableName: string, nodeId?: number, nodeType?: 'category' | 'dataset', inheritedValue?: any) => {
    try {
      // Determine context and composite key
      const existing = changeTracker.getChanges().find(c => c.variableName === variableName
        && ((nodeType ?? 'template') === c.saveContext.saveLevel)
        && ((nodeType ? nodeId : undefined) === c.saveContext.targetId)
      );
      const effectiveContext = existing?.saveContext ?? ({ saveLevel: (nodeType ?? 'template'), targetId: nodeId } as const);
      const key = VariableChangeTracker.buildChangeKey(variableName, effectiveContext);

      // Update local value to inherited for UI
      setVariableChanges(prev => {
        const next = new Map(prev);
        if (inheritedValue !== undefined) {
          next.set(key, inheritedValue);
        } else {
          next.delete(key);
        }
        return next;
      });

      // Use original variable from existing change if present
      const originalVariable = existing?.originalVariable as VariableWithContext | undefined;

      // Add pending reset action to the tracker
      changeTracker.addChange(
        variableName,
        originalVariable?.value,
        undefined,
        originalVariable as any,
        nodeId,
        nodeType,
        'reset'
      );
    } catch (error) {
      console.error('Failed to mark variable as reset locally:', error);
      throw error;
    }
  }, [changeTracker, setVariableChanges]);

  return {
    variableChanges,
    hasChanges,
    updateVariable,
    resetVariable,
    resetAllChanges,
    saveChanges,
    resetToInherited,
    isSaving,
    saveError,
    isResetting,
    resetError,
    getVariableValue,
    isVariableChanged,
    isVariableResetPending
  };
}

