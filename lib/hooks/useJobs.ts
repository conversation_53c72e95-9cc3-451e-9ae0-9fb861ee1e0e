import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '../services/api'

export function useJobs(page?: number, itemsPerPage?: number) { // Renamed hook
    return useQuery({
        queryKey: ['jobs', page, itemsPerPage], // Updated queryKey
        queryFn: () => api.getJobs(page, itemsPerPage),
        refetchInterval: 5000,
        retry: 2
    })
}

export function useRecentJobs(limit: number = 5) {
    return useQuery({
        queryKey: ['recent-jobs', limit],
        queryFn: () => api.getRecentJobs(limit),
        refetchInterval: 30000, // Refresh every 30 seconds for dashboard
        retry: 2
    })
}

export function useJob(id: string) { // Renamed hook
    return useQuery({
        queryKey: ['jobs', id], // Updated queryKey
        queryFn: () => api.getJobById(id),
        refetchInterval: 5000,
        retry: 2
    })
}

export function useCreateJob() { // Renamed hook
    const queryClient = useQueryClient()
    
    return useMutation({
        mutationFn: api.createJob,
        onMutate: async (newJob) => { // Renamed parameter
            await queryClient.cancelQueries({ queryKey: ['jobs'] }) // Updated queryKey
            const previousJobs = queryClient.getQueryData(['jobs']) // Renamed variable, updated queryKey
            
            queryClient.setQueryData(['jobs'], (old: any) => ({ // Updated queryKey
                ...old,
                // Assuming newJob is the object returned by createJob
                data: [...(old?.data || []), { ...newJob, id: 'temp-id', status: 'pending' }]
            }))

            return { previousJobs } // Renamed variable
        },
        onError: (err, _, context) => {
            queryClient.setQueryData(['jobs'], context?.previousJobs) // Updated queryKey, renamed variable
            return err
        },
        onSettled: () => {
            queryClient.invalidateQueries({ queryKey: ['jobs'] }) // Updated queryKey
        }
    })
}