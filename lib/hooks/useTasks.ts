import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query' // Added useMutation, useQueryClient
import { api } from '../services/api'

export function useTasks(jobId: string) { // Renamed function and parameter
    return useQuery({
        queryKey: ['tasks', jobId], // Updated queryKey
        queryFn: () => api.getTasksByJobId(jobId), // Updated queryFn
        refetchInterval: 100,
        retry: 2
    })
}

export function useCreateTask() { // Renamed function
  const queryClient = useQueryClient()

  return useMutation({
      mutationFn: api.createTask, // Updated mutationFn
      onMutate: async (newTask) => { // Renamed parameter
          await queryClient.cancelQueries({ queryKey: ['tasks'] }) // Updated queryKey
          const previousTasks = queryClient.getQueryData(['tasks']) // Renamed variable, updated queryKey

          queryClient.setQueryData(['tasks'], (old: any) => ({ // Updated queryKey
              ...old,
              data: [...(old?.data || []), { ...newTask, id: 'temp-id', status: 'pending' }] // Renamed variable
          }))

          return { previousTasks } // Renamed variable
      },
      onError: (err, _, context) => {
          // Revert the optimistic update only if previousTasks exists in context
          if (context?.previousTasks) { // Renamed variable
            queryClient.setQueryData(['tasks'], context.previousTasks); // Updated queryKey, renamed variable
          }
          return err;
      },
      onSettled: () => {
          queryClient.invalidateQueries({ queryKey: ['tasks'] }) // Updated queryKey
      }
  })
}