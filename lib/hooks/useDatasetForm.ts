import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../services/api';
import { DatasetFormData, Variable, SelectedFile } from '@/app/create-dataset/types';
import { formatVariablesForApi } from '@/app/create-dataset/utils';

export function useDatasetForm(datasetId: string | null, user: any) {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [files, setFiles] = useState([]);
    const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
    const [variables, setVariables] = useState<Variable[]>([]);
    const [isEditing, setIsEditing] = useState(false);
    const [hasValidationError, setHasValidationError] = useState(false);
    const queryClient = useQueryClient();

    const createDataset = useMutation({
        mutationFn: api.createDataset,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['datasets'] });
        }
    });

    useEffect(() => {
        async function fetchFiles() {
            const response = await fetch('/api/file');
            const data = await response.json();
            setFiles(data.files || []);
        }

        async function fetchDataset() {
            if (datasetId) {
                const response = await fetch(`/api/datasets/${datasetId}`);
                const { data } = await response.json();
                if (data) {
                    setName(data.name);
                    setDescription(data.description);
                    setIsEditing(true);
                    // Set other fields as needed
                }
            }
        }

        if (user) {
            fetchFiles();
            if (datasetId) {
                fetchDataset();
            }
        }
    }, [datasetId, user]);

    const validateForm = () => {
        if (!name.trim()) {
            throw new Error('Dataset name is required');
        }

        if (selectedFiles.length === 0) {
            throw new Error('Please select at least one file');
        }

        const hasInvalidVariables = variables.some(v => !v.isValid);
        if (hasInvalidVariables) {
            throw new Error('Please fix invalid variable values before saving');
        }
    };

    const handleSaveDataset = async () => {
        try {
            validateForm();

            const payload = {
                name,
                description,
                filePaths: selectedFiles.map(file => file.path),
                fileTypes: selectedFiles.map(file => file.type),
                variableOverrides: {
                    vars: formatVariablesForApi(variables)
                }
            };

            if (isEditing) {
                return await fetch(`/api/datasets/${datasetId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
            } else {
                return await createDataset.mutateAsync(payload);
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        name,
        setName,
        description,
        setDescription,
        files,
        selectedFiles,
        setSelectedFiles,
        variables,
        setVariables,
        isEditing,
        hasValidationError,
        setHasValidationError,
        handleSaveDataset,
        error: createDataset.error,
    };
}
