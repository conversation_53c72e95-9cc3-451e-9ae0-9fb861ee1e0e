import { create } from 'zustand'
import { TemplateVariable } from '../../components/template/types/template'

export interface ValidationError {
  variableName: string;
  message: string;
}

interface TemplateStore {
  templateVars: TemplateVariable[];
  validationErrors: ValidationError[];
  setTemplateVars: (vars: TemplateVariable[]) => void;
  updateVariableData: (name: string, newData: any) => void;
  setValidationError: (variableName: string, message: string | null) => void;
  clearValidationError: (variableName: string) => void;
  clearAllValidationErrors: () => void;
  hasValidationErrors: () => boolean;
  getValidationError: (variableName: string) => string | null;
}

export const useTemplateStore = create<TemplateStore>((set, get) => ({
  templateVars: [],
  validationErrors: [],
  setTemplateVars: (vars) => set({ templateVars: vars }),
  updateVariableData: (name, newData) => 
    set((state) => ({
      templateVars: state.templateVars.map((variable) =>
        variable.name === name ? { ...variable, data: newData } : variable
      ),
    })),
  setValidationError: (variableName, message) =>
    set((state) => ({
      validationErrors: message 
        ? [...state.validationErrors.filter(e => e.variableName !== variableName), { variableName, message }]
        : state.validationErrors.filter(e => e.variableName !== variableName)
    })),
  clearValidationError: (variableName) =>
    set((state) => ({
      validationErrors: state.validationErrors.filter(e => e.variableName !== variableName)
    })),
  clearAllValidationErrors: () => set({ validationErrors: [] }),
  hasValidationErrors: () => get().validationErrors.length > 0,
  getValidationError: (variableName) => {
    const error = get().validationErrors.find(e => e.variableName === variableName);
    return error ? error.message : null;
  }
}))
